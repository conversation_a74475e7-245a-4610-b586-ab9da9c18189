# Documentação Modelo Preditivo - Inteli

## HotData

### Red Hot Chilli Peppers

#### <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>  

## <PERSON><PERSON><PERSON><PERSON>

<details>
  <summary><a href="#c1">1. Introdução</a></summary>
</details>

<details>
  <summary><a href="#c2">2. Objetivos e Justificativa</a></summary>
  <ul>
    <li><a href="#c2.1">2.1. Objetivos</a></li>
    <li><a href="#c2.2">2.2. Proposta de solução</a></li>
    <li><a href="#c2.3">2.3.Justificativa</a></li>
  </ul>
</details>

<details>
  <summary><a href="#c3">3. Metodologia</a></summary>
</details>

<details>
<summary><a href="#c4">4. Desenvolvimento e Resultados</a></summary>
  <ul>
    <li><a href="#c4.1">4.1.Compreensão do Problema</a></li>
      <ul>
        <li><a href="#c4.1.1">4.1.1. Contexto da indústria</a></li>
        <li><a href="#c4.1.2">4.1.2. Análise SWOT</a></li>
        <li><a href="#c4.1.3">4.1.3. Planejamento Geral da Solução</a></li>
        <li><a href="#c4.1.4">4.1.4. Value Proposition Canvas</a></li>
        <li><a href="#c4.1.5">4.1.5. Matriz de Riscos</a></li>
        <li><a href="#c4.1.6">4.1.6. Personas</a></li>
        <li><a href="#c4.1.7">4.1.7. Jornadas do Usuário</a></li>
        <li><a href="#c4.1.8">4.1.8. Política de Privacidade</a></li>
      </ul>
    <li><a href="#c4.2">4.2. Compreensão dos Dados</a></li>
      <ul>
        <li><a href="#c4.2.1">4.2.1. Exploração de dados</a>
        </li>
        <li><a href="#c4.2.2">4.2.2. Pré-processamento dos dados</a></li>
        <li><a href="#c4.2.3">4.2.3. Hipóteses</a></li>
      </ul>
    <li><a href="#c4.3">4.3. Preparação dos Dados e Modelagem</a></li>
    <li><a href="#c4.4">4.4. Comparação de Modelos</a></li>
    <li><a href="#c4.5">4.5. Avaliação</a></li>
  </ul>
</details>

<details>
  <summary><a href="#c5">5. Conclusões e Recomendações</a></summary>
</details>

<details>
  <summary><a href="#c6">6. Referências</a></summary>
</details>

<details>
<summary>Anexos</a></summary>
</details>

<br>

## <a name="c1"></a>1. Introdução

&ensp;A Chilli Beans é uma empresa brasileira de grande porte, a qual consolidou-se, ao longo de seus mais de 28 anos de atuação, como a maior rede especializada em óculos e acessórios da América Latina. Com mais de 900 pontos de venda espalhados pelo Brasil, além de presença internacional em mais de vinte países. A empresa destaca-se não apenas pelo seu crescimento exponencial, mas também por sua presença marcante no mercado de moda e estilo de vida, com produtos que vão desde óculos de sol e óculos de grau até relógios e acessórios diversos (CHILLI BEANS, 2025).

&ensp;Em território nacional, a empresa ocupa o terceiro lugar entre as maiores redes de óticas, mantendo a liderança no segmento de óculos de sol. Para 2024, sua receita total está projetada em R$ 1,2 bilhão, o que a posiciona como uma referência em inovação, branding e expansão estratégica (FORBES, 2024). A empresa possui três vertentes principais: Chilli Beans, Óticas Chilli Beans e Eco Chilli, sendo cada uma direcionada a diferentes perfis e experiências de consumo. A marca também é reconhecida por seu apelo jovem, ousado e irreverente, utilizando estratégias de colaboração com empresas e temáticas como NASA, Disney, Volkswagen e Risqué, em coleções que visam agregar valor emocional e simbólico aos seus produtos (MYBEST, 2025).

&ensp;Apesar do seu posicionamento consolidado no mercado, a empresa enfrenta desafios relevantes que demandam soluções tecnológicas e preditivas. Dentre os principais, destacam-se: (1) o baixo fluxo e conversão nas unidades físicas de rua da Ótica Chilli Beans, modelo ainda recente e pouco familiar ao público; (2) a necessidade de definir com precisão o perfil ideal de cliente para cada uma das marcas, com vistas a orientar campanhas de comunicação personalizadas e aumentar a assertividade na captação de novos consumidores; e (3) a baixa penetração da categoria de óculos de grau, que apesar da força das armações da marca, ainda não se consolidou como referência nesse nicho. (TAPI CHILLI BEANS 2025)

Fontes:
<sub>TAPI CHILLI BEANS. Material institucional disponibilizado para o projeto preditivo 2025. Inteli, 2025. Acesso em: 07 ago. 2025.</sub>
<sub>FORBES. How Chilli Beans Innovates at the Intersection of Fashion and Tech. 2024. Disponível em: https://www.forbes.com/sites/angelicamarideoliveira/2024/09/02/how-chilli-beans-innovates-at-the-intersection-of-fashion-and-tech/. Acesso em: 07 ago. 2025.</sub>
<sub>MYBEST. Melhores marcas de óculos de sol para comprar em 2025. 2025. Disponível em: https://www.mybest.com.br/14473. Acesso em: 07 ago. 2025.</sub>

## <a name="c2"></a>2. Objetivos e Justificativa

### <a name="c2.1"></a>2.1. Objetivos

```
Descreva resumidamente os objetivos gerais e específicos do seu parceiro de negócios.

Remova este bloco ao final
```

### <a name="c2.2"></a>2.2. Proposta de solução

```
Descreva resumidamente sua proposta de modelo preditivo e como esse modelo pretende resolver o problema, atendendo os objetivos.

Remova este bloco ao final
```

### <a name="c2.3"></a>2.3. Justificativa

```
Faça uma breve defesa de sua proposta de solução, escreva sobre seus potenciais, seus benefícios e como ela se diferencia.

Remova este bloco ao final
```

## <a name="c3"></a>3. Metodologia

```
Descreva a metodologia CRISP-DM e suas etapas de desenvolvimento, citando o referencial teórico. Você deve apenas enunciar os métodos, sem dizer ainda como eles foram aplicados, nem quais resultados foram obtidos.

Remova este bloco ao final
```

## <a name="c4"></a>4. Desenvolvimento e Resultados

### <a name="c4.1"></a>4.1. Compreensão do Problema

#### <a name="c4.1.1"></a>4.1.1. Contexto da indústria

&nbsp;A Chilli Beans está inserida em um setor híbrido que envolve moda, acessórios e, mais recentemente, o mercado óptico. Inicialmente, a marca consolidou-se como referência no segmento de óculos de sol e acessórios com forte apelo fashion, adotando o modelo de fast fashion. Nesse modelo, os produtos são renovados semanalmente com coleções exclusivas e temáticas, mantendo o dinamismo e a constante reinvenção como estratégia de diferenciação frente à concorrência.
&nbsp;A proposta da Chilli Beans é aliar estilo, acessibilidade e inovação em design, o que a posiciona como uma love brand entre o público jovem. Apesar de atuar em um mercado de moda vasto e competitivo, a empresa conquistou um espaço próprio ao investir fortemente em identidade visual, experiência de marca e formatos inovadores de venda, como quiosques em shoppings e ativações em eventos culturais e musicais.
&nbsp;Atualmente, a operação da Chilli Beans é estruturada em três formatos principais: quiosques, lojas tradicionais (as chamadas “lojas vermelhas”) e óticas voltadas para prescrição. Nos últimos anos, a marca passou a explorar de forma estratégica o setor oftalmológico com o lançamento das “Chilli Beans Óticas”. De acordo com o portal Investe SP, a empresa planeja abrir 400 novas óticas de rua até 2025, expandindo sua presença no varejo físico e diversificando o portfólio com foco em óculos de grau e lentes corretivas.
&nbsp;Esse movimento posiciona a marca em um contexto setorial que exige a compreensão tanto das tendências de moda e comportamento jovem quanto das oportunidades do setor óptico — impulsionado pelo envelhecimento populacional e pelo aumento da preocupação com a saúde visual. A capacidade da Chilli Beans de unir moda e funcionalidade configura-se como um diferencial competitivo em um ambiente de constante transformação, onde inovação, agilidade e posicionamento são fatores cruciais.

##### Recorte Setorial e Estrutura de Funcionamento

&nbsp;O setor óptico brasileiro é composto por diferentes elos da cadeia de valor: produção de lentes e armações, montagem e personalização, distribuição e varejo. As redes de óticas representam o elo final e mais visível ao consumidor, exercendo forte influência sobre a escolha das marcas e o posicionamento comercial dos produtos. Essa cadeia pode ser organizada da seguinte forma:

- Indústria de lentes e armações (ex: Luxottica, Essilor)  
- Montadoras e laboratórios ópticos  
- Distribuidores  
- Redes de varejo óptico  
- Pontos de venda multimarcas e franquias  
- Serviços oftalmológicos (prescrição médica)  

&nbsp;A Chilli Beans atua majoritariamente na etapa de varejo, com um modelo baseado em franquias. Suas coleções exclusivas seguem uma lógica semelhante à do fast fashion, aplicando-a ao setor óptico.

##### Fast Fashion e Aplicação ao Setor Óptico

&nbsp;O conceito de fast fashion refere-se a um modelo de negócios voltado para alta rotatividade de coleções, agilidade no lançamento de novos produtos e preços acessíveis. Marcas como Zara e H&M são referências nesse modelo, colocando novidades nas lojas semanalmente e incentivando o consumo contínuo.  
&nbsp;A Chilli Beans adapta esse modelo ao seu contexto ao lançar semanalmente novas coleções de óculos e relógios, muitas vezes com temáticas exclusivas, parcerias com artistas e tiragens limitadas. Essa abordagem híbrida busca conciliar exclusividade e diferenciação simbólica com acessibilidade, evitando o posicionamento premium de marcas como Ray-Ban, Lacoste ou Apple.

##### Principais Players e Disputa de Mercado

&nbsp;O mercado de óticas no Brasil é dominado por grandes redes. Em termos de receita, a Chilli Beans ocupa a terceira colocação, atrás de:

- **Luxottica / Óticas Carol – R$ 877 milhões (2018)**  
- **Óticas Diniz – R$ 847 milhões (2018)**  

&nbsp;Quanto ao número de unidades, a empresa também aparece em terceiro lugar, com 879 lojas, enquanto a Óticas Carol possui aproximadamente 1.200 e a Óticas Diniz, 1.000.  
&nbsp;O segmento de prescrição oftalmológica, três vezes maior que o de óculos de sol, representa uma importante oportunidade de crescimento. A abertura de novas unidades da “Chilli Beans Óticas” reforça a intenção da marca de competir diretamente com as líderes do setor tradicional.

##### Concorrência e Startups

&nbsp;Além das grandes redes, há uma crescente presença de startups ópticas e marcas direct-to-consumer, que oferecem lentes e armações com forte apelo digital, personalização e preços competitivos. Exemplos incluem **LIVO**, **Zerezes** e **Mooptica**. Essas empresas apostam em experiências de compra inovadoras, como prova virtual, e em práticas sustentáveis.  
&nbsp;Embora a Chilli Beans mantenha seu foco principal no varejo físico, também vem investindo em tecnologia e inovação como diferenciais competitivos.

##### Inovação e Transformação Tecnológica

&nbsp;A marca aposta em ferramentas tecnológicas que aprimoram a experiência de compra e reforçam seu posicionamento inovador, tais como:

- Espelhos inteligentes para experimentação virtual de óculos  
- Aplicativos com realidade aumentada  
- Integração com sistemas de prescrição óptica  
- Plataformas omnichannel para unificar os canais físicos e digitais  

&nbsp;Essas iniciativas fazem parte do reposicionamento estratégico da empresa, que visa ampliar sua atuação no setor de saúde visual sem abrir mão de seu apelo jovem e fashion.

##### Estratégia e Posicionamento

A estratégia da Chilli Beans combina elementos como:

- Narrativa emocional e storytelling em cada coleção  
- Tiragens limitadas para gerar escassez e exclusividade  
- Preços acessíveis, evitando a elitização  
- Modelo de franquias que garante ampla presença nacional  
- Alto ritmo de lançamentos, inspirado no fast fashion  

&nbsp;Contudo, essa abordagem pode gerar certa ambiguidade no posicionamento: ao buscar diferenciação simbólica sem adotar preços elevados, a marca ocupa um espaço intermediário — entre o popular e o premium — o que exige clareza na comunicação e segmentação do público-alvo.

##### Considerações Finais

&nbsp;O setor óptico brasileiro é robusto, regulado e competitivo, com margens apertadas e crescente transformação digital. A Chilli Beans destaca-se ao integrar moda e saúde visual em uma proposta única, que valoriza agilidade, inovação e experiência do consumidor. Seu modelo de negócio reflete as transformações do varejo contemporâneo, no qual é necessário combinar valor simbólico, eficiência operacional e conexão com o público.

---

#### Análise das Cinco Forças de Porter

<div align="center">
  <sub>Figura x - 5 Forças de Porter</sub><br>
  <img src="../assets/5_forcas_de_porter.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

##### 1. Ameaça de Novos Entrantes – Moderada

&nbsp;A entrada no setor de moda e acessórios é relativamente fácil do ponto de vista operacional (baixo custo inicial, fornecedores acessíveis e plataformas de venda digital). No entanto, a Chilli Beans já possui uma marca consolidada, forte identidade visual, fidelidade do público e uma estrutura logística ampla (com franquias, quiosques e pontos físicos estrategicamente posicionados), o que representa uma barreira competitiva significativa.

&nbsp;No ramo ótico, por outro lado, as exigências técnicas, regulamentações e necessidade de certificações criam uma barreira mais elevada, favorecendo players já estabelecidos.

**Resumo:** embora novos concorrentes possam surgir, competir com a força da marca e escala da Chilli Beans é desafiador.

---

##### 2. Poder de Barganha dos Fornecedores – Baixo a Moderado

&nbsp;A Chilli Beans mantém controle sobre grande parte da sua cadeia de produção, contando com produção terceirizada, principalmente na Ásia, e design interno exclusivo. Como a empresa opera em larga escala e possui forte poder de marca, ela consegue negociar preços competitivos com fornecedores.

&nbsp;Contudo, como parte da cadeia é internacional, questões logísticas (como flutuação cambial ou crises globais) podem aumentar custos ou comprometer prazos.

**Resumo:** a empresa possui certo poder de barganha, mas é vulnerável a fatores externos globais.

---

##### 3. Poder de Barganha dos Clientes – Moderado

&nbsp;No segmento de moda e acessórios, os clientes possuem diversas alternativas de marcas nacionais e internacionais. No entanto, a Chilli Beans investe fortemente em exclusividade de design, coleções temáticas e experiência de marca, o que reduz a sensibilidade ao preço e promove fidelização.

&nbsp;No setor ótico, o cliente tende a buscar mais confiança e qualidade técnica, o que pode aumentar o poder de decisão do consumidor. Ainda assim, a marca vem se posicionando com diferenciais de estilo e acessibilidade.

**Resumo:** os consumidores têm opções, mas a proposta única da Chilli Beans equilibra esse poder.

---

##### 4. Ameaça de Produtos Substitutos – Alta

&nbsp;Óculos, relógios e acessórios são itens que podem ser facilmente substituídos por outras marcas, genéricos ou produtos de segmentos correlatos (ex: eletrônicos vestíveis).

&nbsp;Além disso, o setor de moda sofre com altas taxas de rotatividade de tendências, o que demanda constante inovação. Em mercados online, a concorrência com grandes e-commerces e marcas digitais torna a substituição ainda mais fácil.

**Resumo:** o risco de substituição é alto, mas mitigado pela identidade e inovação contínua da marca.

---

##### 5. Rivalidade entre Concorrentes – Alta

&nbsp;O mercado de moda e acessórios é extremamente competitivo, com marcas locais e internacionais, incluindo grandes redes como Ray-Ban, Oakley, Lupo, além de marcas de fast fashion que vendem acessórios de baixo custo.

&nbsp;No segmento ótico, há empresas consolidadas como Óticas Carol, Chilli Beans Óticas, e óticas independentes, que disputam mercado com promoções agressivas e parcelamentos.

&nbsp;A Chilli Beans se destaca ao unir moda, exclusividade e presença multicanal (lojas, quiosques e óticas), o que a torna resistente. Porém, precisa manter inovação constante e boa gestão de marca para sustentar sua posição.

**Resumo:** a rivalidade é alta, mas a Chilli Beans possui diferenciais competitivos claros.

---

#### Conclusão Geral

&nbsp;A Chilli Beans atua em um setor de alta competitividade, onde o design exclusivo, posicionamento de marca e agilidade na oferta de produtos são suas maiores fortalezas. Sua estratégia de diferenciação no fast fashion de acessórios, somada à recente expansão no setor óptico, a coloca em um cenário de desafios, mas também de oportunidades.

##### Referências

- ABIÓPTICA. Case de sucesso Chilli Beans: tecnologia, proximidade e inovação. São Paulo: Associação Brasileira da Indústria Óptica, [2017]. Disponível em: <https://www.abioptica.com.br/case-de-sucesso-chilli-beans-tecnologia-proximidade-e-inovacao/>. Acesso em: 6 ago. 2025.

- ABIÓPTICA. Chilli Beans abre nova rede de óticas. São Paulo: Associação Brasileira da Indústria Óptica, [2018]. Disponível em: <https://www.abioptica.com.br/chilli-beans-abre-nova-rede-de-oticas/>. Acesso em: 6 ago. 2025.

- EXAME. Caito Maia e os negócios em expansão da Chilli Beans. São Paulo: Exame, 6 mar. 2024. Disponível em: <https://exame.com/negocios/caito-maia-chilli-beans-negocios-em-expansao-neex-2025/>. Acesso em: 6 ago. 2025.

- INVESTE SP. Plano de expansão da Chilli Beans prevê a abertura de 400 óticas de rua até 2025. São Paulo: Agência Paulista de Promoção de Investimentos e Competitividade, 4 fev. 2024. Disponível em: <https://www.investe.sp.gov.br/noticia/plano-de-expansao-da-chilli-beans-preve-a-abertura-de-400-oticas-de-rua-ate-2025/>. Acesso em: 6 ago. 2025.

#### <a name="c4.1.2"></a>4.1.2. Análise SWOT

&ensp;A análise SWOT é uma ferramenta estratégica essencial para diagnosticar Forças (Strengths) e Fraquezas (Weaknesses) internas, e Oportunidades (Opportunities) e Ameaças (Threats) externas de uma organização. No caso da Chilli Beans, essa análise revela que a marca é um dos principais players do mercado, embora existam pontos que exigem atenção constante por parte de seus gestores.

<div align="center">
  <sub>Figura x - Análise SWOT</sub><br>
  <img src="../assets/swot.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;Em resumo, a Chilli Beans se destaca pela inovação (Shopping Centers 2019), identidade ousada e compromisso com a qualidade e sustentabilidade (VEJA 2022). No entanto, apresenta fragilidades como a dependência de fabricantes internacionais (Isto É 2025) e os desafios para atrair novos clientes e impactar o público ideal para a empresa. Entre as oportunidades, destacam-se a expansão internacional (ABF 2021) e a presença em cidades menores por meio da linha Eco Chilli. Já entre as ameaças, estão o aumento da pirataria e um risco, ainda que baixo, de dano reputacional associado à exposição intensa do fundador Caito Maia nas redes sociais.

Fontes:
<sub>ASSOCIAÇÃO BRASILEIRA DE FRANCHISING. Chilli Beans e o mercado internacional: marca tem planos de expandir ainda mais no exterior em 2022. 2021. Disponível em: https://www.abf.com.br/franchisingbrasil/noticias/chilli-beans-e-o-mercado-internacional-marca-tem-planos-de-expandir-ainda-mais-no-exterior-em-2022/. Acesso em: 7 ago. 2025.</sub>
<sub>VEJA SP. Chilli Beans Eco Chilli. 2022. Disponível em: https://vejasp.abril.com.br/coluna/terraco-paulistano/chilli-beans-eco-chilli/. Acesso em: 7 ago. 2025.</sub>
<sub>MUNDO DO MARKETING. Risqué lança coleção em parceria com Chilli Beans. Disponível em: https://mundodomarketing.com.br/risque-lanca-colecao-em-parceria-com-chilli-beans. Acesso em: 7 ago. 2025.</sub>
<sub>REVISTA SHOPPING CENTERS. Chilli Beans: a marca que faz diferente o tempo todo. 2019. Disponível em: https://revistashoppingcenters.com.br/varejista/chilli-beans-a-marca-que-faz-diferente-o-tempo-todo/. Acesso em: 7 ago. 2025.</sub>
<sub>ISTO É DINHEIRO. Caito Maia Chilli Beans dinheiro entrevista. 2025. Disponível em: https://istoedinheiro.com.br/caito-maia-chilli-beans-dinheiro-entrevista. Acesso em: 7 ago. 2025.</sub>

#### <a name="c4.1.3"></a>4.1.3. Planejamento Geral da Solução

&ensp; O projeto propõe o desenvolvimento de modelos preditivos e analíticos para responder às três problemáticas apresentadas pela Chilli Beans: aumento de fluxo e conversão das Óticas de Rua, definição do público ideal e crescimento da categoria de óculos de grau. Para isso, serão utilizados dados fornecidos pela empresa, contemplando informações de vendas por loja, categorias de produtos, canais de venda, dados geográficos e demográficos, além de variáveis como gênero, idade, localização e histórico de compra dos clientes.

&ensp; A base de dados reúne identificadores, dados de ponto de venda, atributos de clientes e de produtos, são elementos necessários para relacionar registros, calcular métricas, segmentar públicos, comparar formatos de loja e analisar o mix de produtos para recomendações e campanhas.

&ensp; A solução será estruturada com base em técnicas de machine learning e métodos de clusterização, permitindo identificar padrões, segmentar públicos, detectar oportunidades de mercado e sugerir ações estratégicas. No caso das Óticas de Rua, o modelo apontará regiões e unidades com maior potencial de crescimento, considerando fatores socioeconômicos e concorrência. Para o público ideal, o foco será compreender perfis com maior probabilidade de conversão e fidelização, direcionando campanhas e comunicações personalizadas. Já para a categoria de grau, a análise buscará identificar barreiras de conversão, oportunidades de expansão e estratégias para aumentar a penetração dessa linha de produtos.

&ensp; Os resultados serão apresentados por meio de relatórios e visualizações interativas, como mapas geográficos, comparativos entre unidades e mapas de calor por segmento. Essas entregas servirão de apoio para decisões de marketing, expansão e gestão de produtos.

&ensp; Entre os principais benefícios esperados são aumento de visitas e conversão em lojas de rua, maior assertividade nas campanhas de marketing, engajamento de públicos estratégicos, crescimento do market share da categoria de grau e ampliação do ticket médio.

&ensp; O sucesso da solução será medido a partir de indicadores como crescimento percentual no fluxo e conversão das lojas, aumento na taxa de aquisição de clientes, evolução no desempenho da categoria de grau e relevância dos insights para o planejamento estratégico da empresa

#### <a name="c4.1.4"></a>4.1.4. Value Proposition Canvas

&ensp;O Canvas da Proposta de Valor é uma ferramenta visual e estratégica que garante o alinhamento perfeito entre o produto ou serviço oferecido e as reais necessidades do cliente, diminuindo os riscos de criar algo que ninguém quer. A estrutura se divide em dois lados: o Perfil do Cliente, onde se exploram as tarefas, dores e ganhos do público, e o Mapa de Valor, que detalha como os produtos e serviços atuam como "analgésicos" para as dores e "criadores de ganhos". Ao conectar esses dois lados, a ferramenta facilita a criação e comunicação de uma proposta de valor clara, relevante e atrativa. No presente projeto, aplicaremos esta ferramenta para estruturar a proposta de valor da nossa solução para a Chilli Beans.

<div align="center">

  <sub>Figura x - Value Proposition Canvas</sub>

  <img src="/assets/value-proposition-canvas.svg">

  <sup>Fonte: Material produzido pelos autores (2025)</sup>

</div>

##### PROPOSTA DE VALOR

**Produtos & Serviços:**
&ensp;Esta seção detalha os produtos e serviços que compõem a proposta de valor entregue ao cliente.

- **Modelo de Negócio Multicanal:** A proposta contempla diferentes canais de venda e relacionamento, permitindo que o cliente tenha acesso aos produtos de forma prática e adaptada às suas preferências de compra.

- **Parcerias Estratégicas:** A atuação conjunta com parceiros selecionados amplia o alcance e a credibilidade da marca, oferecendo produtos e experiências diferenciadas.

- **Plataformas Digitais Integradas:** O uso de plataformas digitais conectadas garante maior fluidez na jornada do cliente, desde a descoberta até a compra, proporcionando uma experiência unificada.

- **Amplo Portfólio de Produtos:** A variedade de produtos oferecidos atende a diferentes estilos e necessidades, ampliando as possibilidades de escolha e expressão pessoal.

- **Coleções Temáticas:** As coleções criadas com base em tendências ou inspirações específicas tornam a oferta mais atrativa e alinhada ao que o cliente busca no momento.

**Criadores de Ganhos (Gain Creators)**<br>
&ensp;Esta seção detalha os elementos da solução que atuam como criadores de ganhos para o cliente, fortalecendo a proposta de valor.

- **Curadoria de Tendências:** O acompanhamento e a seleção das principais tendências de moda e estilo oferecem ao cliente acesso rápido ao que há de mais atual.

- **Ferramenta de Autoexpressão:** Os produtos e coleções possibilitam que o cliente expresse sua identidade única por meio das peças que escolhe.

- **Exclusividade:** Itens exclusivos fortalecem a sensação de singularidade e aumentam o valor percebido da compra.

- **Senso de Pertencimento:** As ações e campanhas da marca estimulam o sentimento de fazer parte de uma comunidade com interesses e valores compartilhados.

- **Fortalecimento da Imagem Pessoal:** As soluções ajudam o cliente a reforçar sua imagem, transmitindo autenticidade e confiança.

**Aliviadores de Dores (Pain Relievers)**<br>
&ensp;Esta seção apresenta os elementos da solução que atuam como aliviadores das dores identificadas no perfil do cliente.

- **Redução da Insegurança:** As coleções e produtos são desenvolvidos para que o cliente se sinta seguro ao experimentar e adotar novas tendências.

- **Quebra da Monotonia:** A oferta constante de novidades mantém a experiência de compra dinâmica e estimulante.

- **Otimização do Tempo de Busca:** Filtros e organização eficientes facilitam a localização de produtos específicos, reduzindo o tempo gasto na procura.

- **Segurança ao Comprar:** O ambiente de compra garante confiabilidade, evitando riscos e incertezas no processo.

- **Combate à Desatualização:** A atualização constante do portfólio impede que o cliente sinta que está consumindo algo ultrapassado ou fora de moda.

----

##### PERFIL DO CLIENTE (Público-Alvo Ideal)

**Tarefas do Cliente (Customer Jobs)**<br>
&ensp;Esta seção descreve as principais responsabilidades, objetivos e desafios enfrentados pelo cliente, que orientam o desenvolvimento da solução.

- **Expressar sua Identidade**: busca por peças e estilos que transmitam essência e personalidade.

- **Manter-se Atualizado**: acompanhar tendências é essencial para reforçar presença e imagem.

- **Buscar Inspiração de Estilos**: procura por referências que auxiliem na criação e reinvenção do visual.

- **Encontrar Produtos com Preço Acessível**: a relação custo-benefício é decisiva no momento da compra.

- **Construir Imagem Pessoal**: deseja que as escolhas de moda sejam coerentes com a forma como quer ser percebido.

**Ganhos Desejados (Gains)**<br>
&ensp;Esta seção apresenta os ganhos esperados pelo cliente com a adoção da solução.

- **Sentir-se Único**: exclusividade e personalização reforçam a individualidade.

- **Fazer Parte de uma Comunidade**: valorização do sentimento de pertencimento a um grupo com interesses similares.

- **Facilidade para se Manter na Moda**: curadoria e atualizações frequentes facilitam o acompanhamento das tendências.

- **Confiança para Novidades**: conforto para experimentar novas peças e estilos.

- **Experiência de Compra Positiva**: atendimento, qualidade dos produtos e usabilidade das plataformas contribuem para um processo de compra satisfatório.

**Dores (Pains)**<br>
&ensp;Esta seção descreve os principais desafios e obstáculos enfrentados pelo cliente.

- **Medo de Ser Visto como “Básico”**: a falta de originalidade nas roupas e acessórios pode gerar insegurança.

- **Dificuldade em Encontrar Produtos Autênticos**: a saturação do mercado dificulta o acesso a itens que realmente transmitem identidade.

- **Orçamento Limitado**: necessidade de conciliar estilo e qualidade com preços acessíveis.

- **Insegurança em Inovar**: receio de arriscar em estilos diferentes e não ser aceito socialmente.

- **Falta de Tempo**: a rotina corrida limita a disponibilidade para buscar e escolher produtos.

&ensp;Dessa forma, é possível perceber, por meio do modelo apresentado, que a solução proposta não apenas busca aliviar as dores identificadas no perfil do cliente, mas também visa potencializar ganhos relevantes em sua experiência de consumo.

#### <a name="c4.1.5"></a>4.1.5. Matriz de Riscos

&ensp;A matriz de riscos é uma ferramenta fundamental de gestão de projetos que permite a identificação, avaliação e priorização sistemática dos riscos que podem afetar o sucesso do projeto.

&ensp;A matriz facilita a tomada de decisões estratégicas ao categorizar os riscos em diferentes níveis de criticidade, permitindo que as equipes de projeto concentrem seus esforços nas ameaças mais significativas e nas oportunidades de maior valor. A estrutura adotada segrega os riscos em duas categorias principais: ameaças (eventos que podem prejudicar os objetivos do projeto) e oportunidades (eventos que podem agregar valor além do esperado), proporcionando uma visão equilibrada dos cenários possíveis durante a execução do projeto.

<div align="center">  
  <sub>Figura x - Matriz de risco</sub><br>
  <img src="../assets/matriz-risco.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

##### Lista de Riscos Identificados

###### Ameaças

**A1 - Qualidade dos dados insuficiente ou inconsistente:** Dados históricos de clientes podem apresentar lacunas, inconsistências ou baixa qualidade que comprometam a precisão dos modelos preditivos e análises de clusterização.

**A2 - Possível descumprimento da LGPD e normas de proteção de dados:** O uso de dados pessoais para segmentação e personalização pode violar normas de proteção de dados, gerando riscos legais e regulatórios.

**A3 - Modelo Preditivo Ineficaz:** Os algoritmos podem desenvolver vieses que discriminem determinados grupos demográficos, comprometendo a equidade e eficácia das estratégias de marketing.

**A4 - Resistência organizacional às mudanças estratégicas:** Equipes de marketing e vendas podem resistir à adoção de novas abordagens baseadas em dados, preferindo métodos tradicionais.

**A5 - Alteração no escopo do projeto:**  Alterações no escopo podem desviar o projeto dos objetivos iniciais.

###### Oportunidades

**O1 - Descoberta de novos nichos de mercado rentáveis:** A análise de dados pode revelar segmentos de clientes não explorados com alto potencial de conversão e fidelização.

**O2 - Melhoria significativa no retorno das campanhas de marketing:** A personalização baseada em clusters pode aumentar drasticamente a efetividade das campanhas publicitárias e de comunicação.

**O3 - Descoberta de padrões comportamentais inesperados:** Os modelos podem revelar insights comportamentais surpreendentes que abram novas possibilidades de segmentação e estratégias não previstas inicialmente.

**O4 - Expansão para novos segmentos demográficos:** Insights sobre comportamento geracional podem facilitar a penetração em faixas etárias ou regiões com baixa participação atual.

**O5 -  Integração bem-sucedida com sistemas existentes:** Os modelos preditivos podem ser integrados de forma mais eficiente que o esperado com os sistemas atuais de CRM e marketing, facilitando a implementação prática das recomendações.

##### Justificativas das Classificações

###### A1 - Qualidade dos dados

&ensp;Em projetos de análise de dados de varejo, problemas de qualidade são extremamente comuns, incluindo registros duplicados, campos em branco, inconsistências de formato e dados desatualizados. A natureza multi-marca da empresa, envolvendo Ótica Chilli Beans, Chilli Beans e Eco Chilli, aumenta significativamente a probabilidade de inconsistências entre diferentes sistemas de dados. Experiências anteriores em projetos similares demonstram que questões de qualidade de dados ocorrem em praticamente todos os casos, variando apenas em grau de severidade.

&ensp;Embora problemas de qualidade de dados não impeçam completamente a execução do projeto, eles podem reduzir significativamente a precisão dos modelos preditivos e das análises de clusterização. Isso resulta em segmentações menos precisas e estratégias de marketing com eficácia reduzida, impactando moderadamente os resultados esperados sem inviabilizar completamente os objetivos do projeto.

###### A2 - Não conformidade LGPD

&ensp;O uso de dados pessoais para segmentação e personalização é dependente ao escopo do projeto. Com a Lei Geral de Proteção de Dados em pleno vigor e fiscalização crescente por parte das autoridades competentes, existe possibilidade de questões de conformidade surgirem durante o desenvolvimento do projeto. Análises comportamentais detalhadas e segmentação de clientes envolvem necessariamente o tratamento de dados pessoais, requerendo atenção adequada aos aspectos regulatórios.

&ensp;Questões de conformidade com a LGPD podem gerar multas e exigir ajustes nos processos e metodologias do projeto, embora raramente impeçam sua execução completa. As penalidades são geralmente proporcionais à gravidade das infrações e podem ser controladas através de adequações processuais e implementação de medidas corretivas apropriadas.

###### A3 - Modelo Preditivo Ineficaz

&ensp;Algoritmos de machine learning podem apresentar desempenho abaixo do esperado devido a diversos fatores, incluindo dados de treinamento inadequados, seleção inapropriada de variáveis, hiperparâmetros mal ajustados ou vieses nos dados históricos. A probabilidade é considerada moderada porque, embora seja um risco comum em projetos de ciência de dados, pode ser mitigado através de metodologias robustas de desenvolvimento e validação de modelos.

&ensp;Um modelo preditivo ineficaz pode comprometer significativamente os objetivos principais do projeto, resultando em segmentações imprecisas de clientes e estratégias de marketing baseadas em predições incorretas. Isso afetaria diretamente a eficácia das campanhas personalizadas e poderia levar a decisões de negócio inadequadas, impactando substancialmente o retorno sobre investimento esperado do projeto.

###### A4 - Resistência organizacional

&ensp;A Chilli Beans demonstra ser uma marca reconhecidamente inovadora e com orientação para soluções baseadas em dados, conforme evidenciado pela própria iniciativa deste projeto. A probabilidade de resistência organizacional significativa é considerada baixa, especialmente considerando que a implementação pode ser acompanhada de treinamento adequado das equipes e demonstração gradual dos resultados positivos iniciais.
&ensp;Caso a resistência organizacional se materialize, pode causar atrasos significativos na implementação das recomendações geradas pelo projeto e na adoção das novas metodologias de marketing. Isso comprometeria substancialmente o retorno sobre investimento esperado e a efetividade prática dos resultados, impactando diretamente o sucesso do projeto no ambiente operacional real da empresa.

###### A5 - Alteração no escopo do projeto

&ensp;Mudanças no escopo durante o desenvolvimento do projeto podem ocorrer devido a novos requisitos de negócio, alterações nas prioridades organizacionais ou descobertas que levem a redirecionamentos estratégicos. A probabilidade é considerada baixa devido ao nível de planejamento e definição de objetivos específicos já estabelecidos para o projeto, embora mudanças organizacionais sempre sejam possíveis.

&ensp;Alterações no escopo podem gerar retrabalho, necessidade de revisão de metodologias e potencial aumento de prazos e custos do projeto. Embora possa afetar a eficiência da execução e demandar readequação de recursos, raramente compromete os objetivos fundamentais do projeto, especialmente se as mudanças forem bem gerenciadas e incorporadas de forma estruturada ao planejamento.

###### O1 - Novos nichos de mercado

&ensp;Projetos de análise de dados e clusterização frequentemente revelam segmentos de clientes não identificados previamente pelas metodologias tradicionais de análise de mercado. Com a Chilli Beans operando três marcas distintas e possuindo uma ampla base de dados de clientes, há alta probabilidade de descobrir nichos de mercado inexplorados que apresentem características e comportamentos específicos não adequadamente atendidos pelas estratégias atuais.

&ensp;A descoberta de novos nichos de mercado rentáveis pode representar oportunidades significativas de aumento de receita e expansão estratégica da empresa. Estes segmentos podem direcionar o desenvolvimento de novas linhas de produtos, estratégias de comunicação específicas e abordagens de marketing diferenciadas, proporcionando crescimento sustentável e fortalecimento da posição competitiva no mercado de ótica e acessórios.

###### O2 - Retorno melhorado das campanhas

&ensp;A personalização de campanhas de marketing baseada em análise de dados e segmentação de clientes possui histórico comprovado de melhoria na efetividade e retorno sobre investimento. Considerando que este é um dos objetivos principais do projeto, há alta probabilidade de algum nível de melhoria mensurável no desempenho das campanhas publicitárias e de comunicação da empresa, embora o grau específico de melhoria possa variar.

&ensp;A melhoria no retorno sobre investimento das campanhas de marketing tem impacto direto na lucratividade da empresa. Este benefício pode justificar rapidamente o investimento realizado no projeto e proporcionar recursos adicionais para reinvestimento em novas iniciativas de marketing e expansão das estratégias de personalização.

###### O3 - Padrões comportamentais inesperados

&ensp;A descoberta de insights comportamentais genuinamente surpreendentes e reveladores é menos previsível que outros tipos de resultados de análise de dados. A probabilidade depende significativamente da riqueza e diversidade dos dados disponíveis, bem como da sofisticação das técnicas analíticas aplicadas. Embora seja possível, não há garantia de que os padrões descobertos sejam suficientemente inovadores para transformar as estratégias existentes.

&ensp;Padrões comportamentais inesperados podem abrir novas possibilidades de segmentação e estratégias de marketing não contempladas anteriormente. O valor real desta oportunidade depende diretamente de quão aplicáveis e implementáveis sejam os insights descobertos na prática operacional da empresa, podendo gerar vantagens competitivas importantes.

###### O4 - Novos segmentos demográficos

&ensp;Embora a identificação de oportunidades em novos segmentos demográficos seja provável através da análise de dados, o sucesso efetivo na expansão para estes segmentos é mais desafiador. A materialização desta oportunidade requer não apenas a identificação dos segmentos, mas também investimentos adicionais em adaptação de produtos, canais de distribuição e estratégias de comunicação que vão além do escopo direto do projeto de análise de dados.

&ensp;A penetração bem-sucedida em novos segmentos demográficos, particularmente em diferentes faixas etárias ou regiões geográficas, pode representar crescimento da base de clientes e receita da empresa. Esta expansão tem potencial de ampliar o alcance de mercado das marcas Chilli Beans, proporcionando oportunidades de crescimento de longo prazo e diversificação da base de clientes.

###### O5 - Integração com sistemas existentes

&ensp;A integração de modelos preditivos com sistemas existentes de CRM e plataformas de marketing é tecnicamente factível utilizando ferramentas e metodologias modernas disponíveis no mercado. O sucesso desta integração depende principalmente de planejamento técnico adequado e coordenação apropriada entre as equipes de tecnologia e marketing, fatores que estão amplamente sob controle da organização.

&ensp;Uma integração bem-sucedida facilita significativamente a implementação prática dos resultados do projeto, reduz custos operacionais de manutenção e atualização dos modelos, e torna o projeto mais sustentável operacionalmente no longo prazo. Embora não transforme diretamente o modelo de negócio da empresa, proporciona eficiência operacional importante para o aproveitamento contínuo dos benefícios gerados pelo projeto.

#### <a name="c4.1.6"></a>4.1.6. Personas

&ensp;Personas são arquétipos fictícios, mas fundamentados em dados, que representam os diferentes perfis de usuários de um produto, serviço ou sistema. Sua construção baseia-se em pesquisas e análises de comportamentos, necessidades e objetivos de usuários reais, consolidando essas informações em um personagem com quem as equipes de projeto possam se identificar. O propósito fundamental das personas é guiar as decisões de estratégia, experiência de usuário e desenvolvimento, garantindo que a solução final esteja centrada nas pessoas que irão utilizá-la e seja verdadeiramente eficaz para resolver suas dores.

&ensp;Para este projeto, foram desenvolvidas quatro personas que demonstram o valor prático do modelo preditivo. A seguir, apresentamos o perfil e as dores de cada uma.

<div align="center">
<sub>Figura X - Descrição da persona Vanessa</sub>

![Persona vanessa](../assets/secao-4-1-6/persona_vanessa.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;A persona **Vanessa** representa um perfil estratégico na Chilli Beans, responsável por definir regiões prioritárias para abertura de lojas, principalmente das óticas de rua, e direcionamento de campanhas. Como coordenadora de expansão, busca decisões embasadas em dados confiáveis para reduzir riscos, prever resultados e otimizar investimentos, garantindo o melhor desempenho das unidades, especialmente as de rua.

<div align="center">
<sub>Figura X - Descrição da persona Caio</sub>

![Persona Caio](../assets/secao-4-1-6/persona_caio.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;A persona **Caio** representa o perfil de um analista de CRM e Inteligência de Clientes que busca segmentar a base com precisão para criar campanhas personalizadas mais eficazes. Formado em Estatística, ele enfrenta dificuldades para prever o comportamento dos clientes e sente falta de ferramentas preditivas que aproveitem todo o potencial dos dados. Caio precisa de clusters comportamentais refinados e insights visuais claros para apoiar decisões rápidas e estratégicas, fortalecendo seu papel dentro do time de marketing.

<div align="center">
<sub>Figura X - Descrição da persona Mariana</sub>

![Persona Mariana](../assets/secao-4-1-6/persona_mariana.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;A persona **Mariana** representa o perfil de cliente que valoriza estilo e design, mas ainda não percebe a Chilli Beans como especialista em lentes de grau. Apesar de já consumir armações da marca, opta por outras óticas na hora de adquirir lentes, devido à falta de informações claras e segurança na escolha do produto. Seu comportamento reflete um padrão de compra em que a decisão pela lente é tomada separadamente da armação, impactando diretamente a taxa de conversão da categoria de grau.

<div align="center">
<sub>Figura X - Descrição da persona Gabriela</sub>

![Persona Gabriela](../assets/secao-4-1-6/persona_gabriela.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;A persona **Gabriela** representa o perfil de uma jovem universitária que busca óculos estilosos e acessíveis. Ela descobre modelos nas redes sociais, mas prefere experimentar antes de comprar. Pesquisa preços online e visita lojas para testar os produtos. Gabriela valoriza comunicação personalizada e facilidade para saber a disponibilidade nas lojas. A falta de campanhas segmentadas e informações rápidas prejudica sua compra, reduzindo conversão e engajamento com a marca.

&ensp;Assim, as quatro personas — Vanessa, Caio, Mariana e Gabriela — foram desenvolvidas para representar de forma precisa diferentes perfis e necessidades estratégicas da marca. Cada uma traz perspectivas complementares sobre desafios e oportunidades, assegurando que as soluções propostas sejam orientadas por necessidades reais, ampliem a eficácia das ações e mantenham o foco no cliente em todas as etapas do projeto.

#### <a name="c4.1.7"></a>4.1.7. Jornadas do Usuário

```
Posicione aqui seus mapas de jornadas do usuário que utiliza o modelo.

Remova este bloco ao final
```

#### <a name="c4.1.8"></a>4.1.8. Política de Privacidade

&ensp;A Política de Privacidade constitui um documento fundamental que estabelece as diretrizes para o tratamento de dados pessoais por uma entidade. Sua função primordial é oferecer total transparência ao titular dos dados, detalhando quais informações são coletadas, as finalidades que justificam sua utilização, as medidas de segurança adotadas para sua proteção e armazenamento, e as condições sob as quais pode ocorrer o compartilhamento com terceiros. Dessa forma, a política formaliza um compromisso com a privacidade e a segurança, sendo um instrumento essencial para garantir a conformidade com a legislação vigente, notadamente a Lei nº 13.709/2018 (Lei Geral de Proteção de Dados - LGPD).

#### Política de Privacidade - Hot Data

**Informações Gerais**
Esta política de privacidade informa como o projeto Hot Data, desenvolvido pelo grupo Red Hot Chili Peppers, trata os dados recebidos da empresa parceira Chilli Beans. O tratamento de dados está em conformidade com a Lei nº 13.709/2018 – Lei Geral de Proteção de Dados (LGPD).

**Dados Coletados**
O projeto utilizará uma base de dados fornecida pela empresa parceira Chilli Beans no que tange informações de vendas por loja, tipo de produto e informações não sensíveis de consumidores. Os dados utilizados para a construção dos modelos analíticos incluem:

- **Dados de Transação:** Identificadores de vendedor, cliente e produto, data da venda, número do pedido, quantidade, histórico de compra e valor dos itens.

- **Dados de Produto:** Categoria do produto, referência e nome do produto, coleção (Griffe) e tipo de marca (Canal).

- **Dados Demográficos e Geográficos dos Clientes:** Gênero declarado, idade (calculada a partir da data de nascimento), cidade e estado do cliente.

- **Dados de Loja (Ponto de Venda):** Identificação da loja, nome da loja e tipo de ponto de venda (Rua, quiosque, shopping, etc.).

Em conformidade com as restrições do projeto, nenhum dado sensível como CPF, nome completo, endereços completos, dados clínicos ou CNPJ será utilizado.

**Finalidade do Tratamento**
Os dados serão tratados com as seguintes finalidades, estritamente acadêmicas e vinculadas aos objetivos do projeto:

- **Para as Óticas de Rua:** Desenvolver um modelo preditivo para apontar regiões e unidades com maior potencial de crescimento, visando o aumento de fluxo e conversão.

- **Para o Público Ideal:** Aplicar métodos de clusterização para segmentar e compreender os perfis de clientes com maior probabilidade de conversão e fidelização, permitindo o direcionamento de campanhas e comunicações personalizadas.

- **Para a Categoria de Grau:** Construir um modelo analítico para identificar barreiras de conversão, oportunidades de expansão e estratégias para aumentar a penetração da linha de produtos de grau.

Os dados recebidos _não serão utilizados para treinar ou alimentar outras ferramentas de Inteligência Artificial externas_ ao escopo do projeto, garantindo a proteção das informações da marca.

**Armazenamento, Retenção, Compartilhamento e Segurança**

- **Armazenamento e Retenção:** Os dados serão armazenados em ambiente de nuvem seguro, providenciado pela instituição de ensino Inteli, com acesso restrito aos membros do grupo de desenvolvimento. Os dados serão mantidos apenas durante o ciclo de vida do projeto e serão permanentemente excluídos após a sua conclusão, expondo apenas o código base para os modelos preditivos no site do Inteli (open source).

- **Compartilhamento de Dados:** Os dados brutos não serão compartilhados com terceiros. Os resultados do projeto (análises, relatórios e dashboards com dados agregados e anonimizados) serão compartilhados apenas com a empresa parceira Chilli Beans e com o corpo docente do Inteli para fins avaliativos.

- **Segurança dos Dados:** Medidas de segurança como controle de acesso rigoroso, uso de senhas fortes e armazenamento em plataformas seguras são adotadas para proteger os dados contra acesso não autorizado, alteração, divulgação ou destruição.

**Direitos dos Titulares e Contato do DPO**

- **Direitos dos Titulares:** Como o projeto utiliza uma base de dados fornecida pelo parceiro, as solicitações de direitos dos titulares, como acesso, correção e exclusão de dados, devem ser direcionadas diretamente aos canais de atendimento da Chilli Beans.

- **Encarregado de Dados (DPO) do Projeto:** Para qualquer dúvida sobre como este projeto trata os dados, o contato é:

  - **Nome:** Suporte Chilli Beans
  - **Contato:** +55 (11) 3173-2050
  - **E-mail:** <<EMAIL>>

### <a name="c4.2"></a>4.2. Compreensão dos Dados

#### <a name="c4.2.1"></a>4.2.1. Exploração de dados

#### Estatística Descritiva das Colunas

##### Seleção das Colunas Úteis

&nbsp; A definição das colunas analisadas não se baseou apenas na disponibilidade do dataset, mas sim em **critérios de relevância estatística e impacto para a tomada de decisão**.  
&nbsp;Os principais critérios adotados foram:

- **Representatividade de métricas financeiras e operacionais**  
  - &nbsp; Colunas como `Valor_total`, `Preco_Varejo` e `DESCONTO_CALCULADO` foram incluídas por refletirem diretamente a receita gerada, as margens de lucro e as estratégias de preço aplicadas.  
  - &nbsp; Variáveis como `Quantidade` e `Data_Venda` permitem avaliar o volume de vendas e a sazonalidade, possibilitando análises de desempenho por produto, loja ou canal.  

- **Segmentação de clientes e comportamento de consumo**  
  - &nbsp; Informações sobre os clientes (`Dim_Cliente.Cidade_cliente`, `Dim_Cliente.Uf_Cliente`, `Dim_Cliente.Genero`, `Dim_Cliente.Data_Nascimento`) foram consideradas relevantes para identificar padrões geográficos e demográficos de consumo.  
  - &nbsp; A derivação da **faixa etária** a partir da data de nascimento possibilita cruzamentos com tipos de produto, permitindo compreender preferências por idade.  

- **Análise de canais e pontos de venda**  
  - &nbsp; Colunas referentes às lojas (`Tipo_PDV`, `CANAL_VENDA`) permitem avaliar a performance por canal de venda (lojas físicas, quiosques e e-commerce), comparando resultados entre diferentes modelos de operação.  

- **Classificação de produtos**  
  - &nbsp; A coluna `Dim_Produtos.Grupo_Produto` permite categorizar os itens vendidos, possibilitando análises de performance por categoria e identificação de produtos estratégicos para a empresa.  

&nbsp; Em resumo, a seleção destas colunas buscou garantir que todas as dimensões críticas — **vendas, clientes, produtos e canais** — fossem contempladas, permitindo análises completas que subsidiam decisões estratégicas de precificação, marketing e expansão comercial.  

---

##### Agregação dos Dados

&nbsp;Após a seleção, realizamos a **clusterização das variáveis em numéricas e categóricas**:

- **Colunas Numéricas:**
 &nbsp;Incluem variáveis como `Preco_Custo`, que representa o custo de produção de cada produto, e `Desconto_Calculado`, que indica o percentual de desconto aplicado. Outras variáveis analisadas, como `Quantidade` e `Valor_Total`, foram fundamentais para a avaliação de volume e receita.  

- **Colunas Categóricas:**
&nbsp;Englobam identificadores de funcionário, cliente e loja, além de atributos como `Dim_Produtos.Grupo_Produto`, `Dim_Produtos.Grupo_Produto` e `REGIAO_CHILLI`. Estas variáveis permitiram segmentar os dados em diferentes dimensões de análise, como perfil de público e desempenho por ponto de venda.  

&nbsp; Essa separação possibilitou aplicar **estatísticas descritivas adequadas a cada tipo de dado**:  

- &nbsp; Para variáveis **numéricas**, foram calculadas média, mediana, moda, desvio padrão, valores mínimos e máximos.  
- &nbsp; Para variáveis **categóricas**, foram analisadas as frequências absolutas e relativas das categorias.  

---

##### Estatística descritiva das colunas

&nbsp;Escolheram-se quatro colunas numéricas do conjunto de dados para a realização da análise estatística: `Preco_Custo`, `DESCONTO_CALCULADO`, `Quantidade` e `Valor_Total`. O objetivo é compreender o comportamento dessas variáveis por meio de medidas descritivas, tais como **média, mediana, moda, desvio padrão, valores mínimos e máximos**, além da soma total e da contagem de registros. Essa análise fornece uma visão geral da distribuição e variabilidade dos dados, servindo como base para interpretações mais aprofundadas e para a identificação de possíveis padrões ou anomalias. [Clique aqui](../notebooks/estatistica_descritiva.ipynb) para acessar o notebook com o código completo.

<div align="center">
  <sub>Tabela X - Tabela de estatística descritiva</sub>
</div>

| Estatística        | Preco_Custo       | DESCONTO_CALCULADO | Quantidade      | Valor_Total       |
|--------------------|------------------:|-------------------:|----------------:|------------------:|
| Média              | 76.37             | 8.05               | 1.09            | 305.63            |
| Mediana            | 73.26             | 0.00               | 1.00            | 322.14            |
| Moda               | 69.21             | 0.00               | 1.00            | 399.98            |
| Desvio Padrão      | 75.19             | 56.36              | 0.33            | 316.62            |
| Soma Total         | 1,402,644.88      | 147,769.52         | 20,024.00       | 5,613,492.68      |
| Mínimo             | 1.80              | -81.74             | 1.00            | 0.01              |
| Máximo             | 1,379.16          | 3,018.01           | 12.00           | 11,057.97         |
| Contagem Valores   | 18,367            | 18,367             | 18,367          | 18,367            |

<div align="center">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

---

##### Gráficos e Visualizações

&ensp;Através de gráficos e visualizações, buscamos compreender as relações e padrões nos dados, identificando insights relevantes para a tomada de decisão.

##### 1. Ticket médio por canal de vendas e por região nas óticas

&nbsp;O ticket médio representa o valor médio de cada compra, sendo uma métrica fundamental para avaliar o poder de geração de receita de cada canal de venda. A análise por região permite identificar padrões geográficos de consumo e desempenho dos canais. Estes insights são focados na Problemática 1: Aumento de fluxo e conversão das Óticas de Rua.
&ensp;Através da análise do ticket médio por canal de vendas e por região, é possível identificar onde há maior volume de vendas e onde o ticket médio é mais elevado, permitindo direcionar esforços de marketing e expansão para as regiões mais promissoras.
&ensp;As colunas `Dim_Lojas.CANAL_VENDA`, `Dim_Lojas.REGIAO_CHILLI`, `Valor_Total` e `Quantidade` foram consideradas relevantes para essa análise. Mais detalhes podemos encontrar no notebook [graph1.ipynb](../notebooks/graph1.ipynb).

<div align="center">
<sub>Figura X - Gráfico de ticket médio por canal de vendas e por região</sub>

![Imagem_Ticket médio por canal de vendas e por região](../assets/Ticket_regiao_canal.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

---

##### 2. Volume de vendas por tipo de produto e agregado por faixa etária

&ensp;A análise da quantidade de produtos por tipo de produto e faixa etária permite entender as preferências de compra de diferentes grupos etários. Esta análise é fundamental para a Problemática 2: Definição do perfil ideal de cliente para cada uma das marcas, com vistas a orientar campanhas de comunicação personalizadas e aumentar a assertividade na captação de novos consumidores.
&ensp;Através da análise da quantidade de produtos por tipo de produto e faixa etária, é possível identificar as preferências de compra de diferentes grupos etários e entender se determinados segmentos apresentam maior aderência a categorias específicas.
&ensp;As colunas `Dim_Produtos.Grupo_Produto` e `Dim_Cliente.Data_Nascimento` foram consideradas relevantes para essa análise. Mais detalhes podemos encontrar no notebook [graph2.ipynb](../notebooks/graph2.ipynb).

<div align="center">
<sub>Figura X - Gráfico de distribuição de compras por faixa etária e grupo de produto</sub>

![Imagem_quantidade de produtos por tipo de produto e faixa etária](../assets/idade_quantida_tipo.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

---

##### 3. Volume de vendas por tipo de loja de ótica e por produto

&ensp;A análise do volume de vendas por tipo de loja de ótica e por produto permite entender a performance de cada tipo de loja em diferentes categorias de produtos. Esta análise é fundamental para a Problemática 3: Crescimento da categoria de óculos de grau, visando identificar oportunidades de expansão e estratégias para aumentar a penetração dessa linha de produtos.
&ensp;Através da análise do volume de vendas por tipo de loja de ótica e por produto, é possível identificar as tendências de consumo em diferentes tipos de lojas e comparar as vendas de óculos de grau em cada um desses contextos.
&ensp;As colunas `Dim_Lojas.Tipo_PDV`, `Dim_Produtos.Sub_Grupo` e `Quantidade` foram consideradas relevantes para essa análise. Mais detalhes podemos encontrar no notebook [graph3.ipynb](../notebooks/graph3.ipynb).

<div align="center">
<sub>Figura X - Gráfico de valor total de vendas por tipo de loja e produto</sub>

![Imagem_Valor total de vendas por tipo de loja e produto](../assets/valor_loja_produto.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

---

#### Principais Insights

- &nbsp; A categoria de **óculos** manteve-se consistentemente como a mais vendida entre todas as faixas etárias. Essa distribuição segue um formato aproximadamente normal, com maior concentração de vendas na faixa etária **30 a 39 anos**.  

- &nbsp; As **Echo Chilli** apresentam um **ticket médio mais elevado** nas regiões **Norte e Nordeste**, indicando um potencial de valorização nesses mercados.  

- Já as lojas **Óticas** e **Vermelhas** apresentam desempenho mais **uniforme em todas as regiões**, sugerindo uma estabilidade maior no perfil de consumo desses canais.

&ensp;Com essa análise, podemos entender melhor os dados e melhorar o nosso modelo preditivo. Entender os dados nos permite entender melhor o comportamento do cliente e, assim, criar um modelo mais preciso e eficiente.

#### <a name="c4.2.2"></a>4.2.2. Pré-processamento dos dados

##### O que é Pré-processamento de Dados?

&ensp;O pré-processamento de dados é como "preparar os ingredientes antes de cozinhar". Assim como um chef precisa lavar, cortar e temperar os ingredientes antes de fazer um prato, precisamos limpar, organizar e transformar os dados antes de criar modelos preditivos.

**Por que é necessário?**

- **Qualidade dos dados:** Dados "sujos" (com erros, valores faltantes ou inconsistências) produzem modelos imprecisos
- **Compatibilidade:** Diferentes algoritmos precisam que os dados estejam em formatos específicos
- **Performance:** Dados bem preparados fazem os modelos aprenderem melhor e mais rápido
- **Confiabilidade:** Resultados mais precisos e confiáveis para tomada de decisões de negócio

**Impacto na qualidade dos modelos:**
&ensp;Um modelo treinado com dados mal preparados é como tentar dirigir com o para-brisa sujo, você pode chegar ao destino, mas com muito mais risco de erro. O pré-processamento adequado pode melhorar a precisão dos modelos em 20-30% ou mais.

##### Metodologia Aplicada

&ensp;Antes de fazermos o pré-processamento, fizemos uma limpeza prévia no banco de dados, removendo registros inconsistentes e duplicados com o módulo [data_filtering.py](../data_filtering.py). Depois, aplicamos transformações nos dados com o módulo [data_preprocessing.py](../data_preprocessing.py) para torná-los adequados para nosso modelo preditivo.

**Implementação Prática:**

- **Notebook de Filtragem:** [filtering.ipynb](../notebooks/filtering.ipynb) - Documentação detalhada dos filtros aplicados
- **Notebook de Pré-processamento:** [proccess_data.ipynb](../notebooks/proccess_data.ipynb) - Implementação e visualização das transformações
- **Módulos Reutilizáveis:** Código modularizado para aplicação consistente em múltiplos notebooks

##### Filtragem Inicial dos Dados

&ensp;Dado a base de dados fornecida pela empresa parceira Chilli Beans, foram identificadas inconsistências e erros nos dados que comprometem a qualidade dos modelos preditivos. Por exemplo, foram encontrados registros de clientes com idades inválidas, preços de produtos inconsistentes e registros duplicados.

&ensp;Comparação de Ano de Nascimento antes e depois da limpeza:

<div align="center">
  <sub>Figura x - Ano de Nascimento Antes</sub><br>
  <img src="../assets/nascimento_antes.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

<div align="center">
  <sub>Figura x - Ano de Nascimento Depois</sub><br>
  <img src="../assets/nascimento_depois.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;Este é um exemplo de como a limpeza dos dados pode afetar a qualidade dos modelos preditivos. Portanto, implementamos uma série de filtros adicionais para garantir a qualidade dos dados. O módulo [data_filtering.py](../data_filtering.py) implementa filtros de negócio essenciais que reduziram o dataset de **40.291** para **18.367** registros (-54,41%), removendo:

- Transações de devolução de mercadoria
- Registros com preços inconsistentes
- Clientes com idades inválidas (fora do intervalo 18-100 anos)
- Duplicatas e registros com dados incompletos de lojas/produtos
- E mais...

&ensp;Abaixo, você pode conferir um resumo dos filtros aplicados e seu impacto no dataset:

| Filtro Aplicado | Registros Restantes | Registros Removidos | % Removido | % Restante |
|---|---|---|---|---|
| Dados iniciais | 40.291 | 0 | 0,00% | 100,00% |
| Remoção DEVOLUÇÃO DE MERCADORIA | 39.010 | 1.281 | 3,18% | 96,82% |
| Preço Varejo > 1 | 38.543 | 467 | 1,16% | 95,66% |
| Total Preço Varejo > 1 | 38.543 | 0 | 0,00% | 95,66% |
| Validação cálculo valor total | 38.543 | 0 | 0,00% | 95,66% |
| Idade entre 10 e 100 anos | 19.288 | 19.255 | 47,79% | 47,87% |
| Idade >= 18 anos no cadastro | 18.694 | 594 | 1,47% | 46,40% |
| Remoção duplicatas | 18.598 | 96 | 0,24% | 46,16% |
| Validação valor total (tolerância 2) | 18.598 | 0 | 0,00% | 46,16% |
| Lojas com Tipo PDV válido | 18.367 | 231 | 0,57% | 45,59% |
| Produtos com nome válido | 18.367 | 0 | 0,00% | 45,59% |
| Produtos com grupo válido | 18.367 | 0 | 0,00% | 45,59% |

&ensp;Resultado final: **18.367** registros, representando **45,59%** dos dados originais.
&ensp;Com esses filtros, garantimos a qualidade dos dados e eliminamos registros inconsistentes, preparando o dataset para modelagem preditiva. Mais detalhes sobre cada filtro podem ser encontrados no módulo [data_filtering.py](../data_filtering.py).

##### Pré-Processamento dos Dados

&ensp;Com o dataset filtrado, seguimos uma metodologia rigorosa para preparar-lo para a modelagem preditiva. O módulo [data_preprocessing.py](../data_preprocessing.py) implementa as seguintes transformações:

**1. Tratamento de Valores Ausentes**

**O que são valores ausentes?**
&ensp;Valores ausentes (ou "missing values") são informações que deveriam estar presentes nos dados mas estão faltando. É como ter fichas de clientes incompletas, algumas podem estar sem telefone, outras sem endereço. Esses "buracos" nos dados podem prejudicar a análise.

&ensp;Foram identificadas 10 colunas com valores ausentes, totalizando 13.701 valores faltantes. As estratégias aplicadas foram:

| Tipo de Coluna | Estratégia | Justificativa |
|---|---|---|
| Colunas com >50% ausentes | Remoção da coluna | Informação insuficiente para modelagem |
| Categóricas | Imputação com moda | Preserva distribuição original |
| Numéricas | Imputação com mediana | Robusta a outliers |
| Outros tipos | Remoção de registros | Quando imputação não é apropriada |

<div align="center">
  <sub>Figura x - Tratamento de Valores Ausentes</sub><br>
  <img src="../assets/valores_ausentes.png">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

**2. Detecção e Tratamento de Outliers**

**O que são outliers?**
&ensp;Outliers são valores "estranhos" ou extremos nos dados, como encontrar uma pessoa de `2,50m` de altura numa pesquisa sobre brasileiros (a média é `1,70m`). Esses valores podem ser erros de digitação ou casos realmente excepcionais, mas podem "confundir" os modelos preditivos.

**Exemplo prático:** Se a maioria dos produtos custa entre `R$ 50-200`, mas alguns custam `R$ 50.000`, esses valores extremos são outliers.

**O que é IQR (Interquartile Range)?**
&ensp;O IQR é uma técnica estatística que funciona como um "detector de valores estranhos". Imagine que você organize todos os valores em ordem crescente e os divida em 4 grupos iguais:

- **Q1 (1º quartil):** 25% dos dados estão abaixo deste valor
- **Q3 (3º quartil):** 75% dos dados estão abaixo deste valor
- **IQR = Q3 - Q1:** A "faixa normal" onde estão 50% dos dados centrais

**Fórmula de detecção:**

- **Limite inferior:** $Q_1 - 1.5 × IQR$
- **Limite superior:** $Q_3 + 1.5 × IQR$
- **Outliers:** Valores fora destes limites

Utilizamos o método IQR para detectar outliers nas principais colunas numéricas. Conforme implementado no notebook [proccess_data.ipynb](../notebooks/proccess_data.ipynb), o processamento removeu **499 registros** (2,72%) devido a outliers extremos, aplicando estratégias adaptativas:

| Coluna | Outliers Detectados | Percentual | Tratamento Aplicado |
|---|---|---|---|
| Quantidade | $1.536$ | $8.36$% | Substituição por percentis 5% e 95% |
| Preco_Custo | $402$ | $2.19$% | Remoção de registros extremos |
| Valor_Total | $373$ | $2.09$% | Remoção de registros extremos |
| Preco_Varejo | $174$ | $0.95$% | Mantidos (impacto negligível) |
| Frete | $2$ | $0.01$% | Mantidos (impacto negligível) |
| Desconto | $2.647$ | $14.41$% | Cap truncation aplicado |

**Critérios de Tratamento:**

**Cap Truncation (Winsorização):** É como "cortar as pontas" dos valores extremos. Em vez de remover os outliers, limitamos eles aos valores máximo e/ou mínimo aceitáveis. É como dizer: "se alguém declarou idade de 200 anos, vamos considerar 100 anos (o máximo razoável)".

- **>8% outliers:** Cap truncation para preservar informação (muitos outliers = podem ser válidos)
- **5-8% outliers:** Substituição por percentis 5% e 95% (valores menos extremos)
- **1-5% outliers:** Remoção de registros extremos (poucos casos = provavelmente erros)
- **<1% outliers:** Manutenção (impacto pequeno nos resultados)

<div align="center">
  <sub>Figura x - Remoção de Outliers</sub><br>
  <img src="../assets/outliers.png">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

**3. Normalização e Padronização**

**Por que transformar a escala dos dados?**
&ensp;Imagine comparar a altura de pessoas (em metros: 1,70) com seus salários (em reais: 5.000). Os algoritmos podem dar mais importância aos salários simplesmente porque os números são maiores. É como comparar maçãs com elefantes, precisamos colocar tudo na mesma "unidade de medida".

**Normalização Min-Max (0-1):**
&ensp;Transforma todos os valores para uma escala de 0 a 1, como converter notas de diferentes provas (0-10, 0-100, 0-20) todas para uma escala de 0 a 1.

**Fórmula:** $\frac{(x - min)}{(max - min)}$

- **x:** valor original
- **min:** menor valor da coluna
- **max:** maior valor da coluna
- **Resultado:** Todos os valores ficam entre 0 e 1

**Padronização Z-Score (μ=0, σ=1):**
&ensp;Transforma os dados para ter média zero e desvio padrão 1. É como "centralizar" os dados em torno da média. Serve para comparar variáveis com escalas diferentes, como idade (anos) e renda (reais).

**Fórmula:** $\frac{(x - \mu)}{\sigma}$

- **x:** valor original
- **μ (mu):** média dos valores
- **σ (sigma):** desvio padrão
- **Resultado:** Média = 0, desvio padrão = 1

<div align="center">
  <sub>Figura x - Visualização Gráfica do Escalonamento</sub><br>
  <img src="../assets/escalonamento.png">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

**4. Codificação de Variáveis Categóricas**

**Por que codificar variáveis categóricas?**
&ensp;Computadores entendem números, não palavras. É como traduzir "São Paulo", "Rio de Janeiro", "Minas Gerais" para uma linguagem que os algoritmos conseguem processar.

**One-Hot Encoding (Variáveis Nominais):**
&ensp;Usado quando as categorias não têm ordem natural (como: primeiro, segundo, terceiro). Cria uma coluna chamada "dummy" para cada categoria com valores 0 ou 1.
&ensp;Colunas dummy são variáveis binárias (0 ou 1) que representam a presença ou ausência de uma categoria específica. O termo "dummy" significa "fictício" ou "artificial" - são colunas criadas artificialmente para representar informações categóricas em formato numérico.

**Exemplo:** Estado Civil

- Original: ["Solteiro", "Casado", "Divorciado"]
- Após One-Hot:
  - Solteiro: $[1, 0, 0]$
  - Casado: $[0, 1, 0]$
  - Divorciado: $[0, 0, 1]$

**Label Encoding (Variáveis Ordinais):**
Usado quando as categorias têm ordem natural (como tamanhos: P, M, G). Atribui números sequenciais.

**Exemplo:** Tamanho

- Original: $["P", "M", "G"]$
- Após Label: $P=1,\, M=2,\, G=3$

**One-Hot Encoding (Variáveis Nominais):**

- `Dim_Lojas.Tipo_PDV`: 15 categorias → 14 colunas dummy
- `Dim_Lojas.CANAL_VENDA`: 3 categorias → 2 colunas dummy
- `Dim_Lojas.REGIAO_CHILLI`: 5 categorias → 4 colunas dummy
- `Dim_Cliente.Uf_Cliente`: 27 categorias → 26 colunas dummy
- `Dim_Produtos.Grupo_Produto`: 12 categorias → 11 colunas dummy
- `Dim_Produtos.Sub_Grupo`: 40 categorias → Top 10 + "Outros" → 10 colunas dummy

**Label Encoding (Variáveis Ordinais):**

- `Dim_Cliente.Sexo`: $M=1,\, F=0,\, S=2,\, ""=3$
- `Dim_Cliente.Estado_Civil`: Mapeamento automático por frequência

##### Resultados do Pré-processamento

Conforme documentado nos notebooks [filtering.ipynb](../notebooks/filtering.ipynb) e [proccess_data.ipynb](../notebooks/proccess_data.ipynb):

| Métrica | Antes | Depois |
|---|---|---|
| **Registros** | $40.291$ | $17.868$ |
| **Colunas** | $66$ | $126$ (+60 novas) |
| **Valores Ausentes** | $13.701$ | $0$ |
| **Outliers Tratados** | $4.311$ | $499$ registros removidos |
| **Variáveis Normalizadas** | $0$ | $6$ colunas (Min-Max + Z-Score) |
| **Variáveis Codificadas** | $0$ | $8$ variáveis categóricas → $48$ colunas dummy |
| **Retenção Final** | - | $44,35$% dos dados originais |

##### Impacto do Pré-processamento

<div align="center">
  <sub>Figura x - Visão Geral da Transformação dos Dados</sub><br>
  <img src="../assets/proccessing_resume.png">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

**Benefícios Alcançados:**

1. **Qualidade dos Dados:** Eliminamos $13.701$ valores ausentes e $4.311$ outliers que poderiam gerar previsões incorretas
2. **Consistência:** Todas as variáveis numéricas agora estão na mesma escala, evitando que algumas dominem outras
3. **Compatibilidade:** Variáveis categóricas foram convertidas para formato numérico, permitindo uso em qualquer algoritmo
4. **Confiabilidade:** Dados limpos e padronizados resultam em modelos mais precisos e confiáveis

&ensp;Concluindo, o pré-processamento dos dados teve um impacto significativo na qualidade e confiabilidade dos modelos preditivos, preparando-os para fornecer insights valiosos e estratégicos para a Chilli Beans.

#### <a name="c4.2.3"></a>4.2.3. Hipóteses

Nesta seção são descritas as hipóteses de pesquisa formuladas a partir das problemáticas levantadas.  
O objetivo é verificar, por meio de análises estatísticas, se existem relações significativas entre variáveis relevantes do negócio e os resultados observados.  
Cada hipótese foi estruturada com definição do teste estatístico, justificativa da formulação e interpretação dos resultados, de modo a apoiar a tomada de decisão com base em evidências quantitativas.  

---

### Hipótese para problemática 1 — Fluxo e Conversão das Óticas de Rua  

**Hipótese de Pesquisa**  
- H₀: O tipo de PDV **não influencia** a quantidade vendida.  
- H₁: O tipo de PDV **influencia** significativamente a quantidade vendida. 

**Two-tailed test:**
O interesse não é verificar se um tipo de PDV vende *mais* ou *menos* especificamente, mas sim se **existe qualquer diferença** entre os tipos.  
  ➝ Como a diferença pode ser para cima ou para baixo, o teste é bilateral.  

$$
H_{0}: \mu_{PDV1} = \mu_{PDV2} = \dots = \mu_{PDVn}
$$

$$
H_{1}: \exists \; i,j \;\; | \;\; \mu_{i} \neq \mu_{j}
$$

**Significados:**
- $\mu_{PDVk}$: média da quantidade vendida no tipo de PDV $k$.  
- $i, j$: índices representando diferentes tipos de PDV.  
- $H_{0}$: todas as médias são iguais.  
- $H_{1}$: pelo menos uma média difere de outra.   

**Justificativa**  
As lojas de rua são um modelo novo e podem apresentar comportamento de vendas distinto em relação a shoppings, quiosques e formato Eco.  
Validar estatisticamente essa hipótese é essencial para compreender a performance de cada canal e orientar estratégias comerciais.  

**Evidência**  
<div align="center">
<sub>Figura X - Output hipótese 1

![Hipótese 1](../assets/secao-4-2-3/hypothesis1.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div> 

**Interpretação**  
O teste ANOVA indicou diferença significativa entre os tipos de PDV (p < 0.001).  
Dessa forma, a hipótese nula (H₀) foi rejeitada e a hipótese alternativa (H₁) aceita, concluindo-se que o tipo de PDV influencia a quantidade vendida.  
Verificou-se que as lojas de rua, especialmente aquelas com perfil ótico, apresentam em média maior número de unidades vendidas por compra em comparação aos demais canais, o que sugere um comportamento distinto nesse formato.  


---

### Hipótese para problemática 2 — Público Ideal  

**Hipótese de Pesquisa**  
- H₀: A idade do cliente **não está associada** ao ticket médio.  
- H₁: A idade do cliente **está associada** ao ticket médio.  

**Two-tailed test:** A hipótese alternativa não define se o ticket médio **aumenta** ou **diminui** com a idade, apenas se existe associação.  
  ➝ Como a correlação pode ser positiva ou negativa, o teste é bilateral.  

$$
H_{0}: \rho = 0
$$

$$
H_{1}: \rho \neq 0
$$

**Significados:**
- $\rho$: coeficiente de correlação populacional entre idade e ticket médio.  
- $H_{0}$: não há correlação (idade não está associada ao ticket médio).  
- $H_{1}$: existe correlação (idade está associada ao ticket médio).  

**Justificativa**  
Entender a relação entre idade e gasto médio é relevante para verificar se determinados grupos geracionais concentram maior valor de compra, ajudando a identificar ou refutar a ideia de um “público ideal” para campanhas direcionadas.  

**Evidência**  
<div align="center">
<sub>Figura X - Output hipótese 2

![Hipótese 2](../assets/secao-4-2-3/hypothesis2.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div> 

**Interpretação**  
A correlação obtida foi r ≈ -0.03 (p < 0.001).  
Apesar de estatisticamente significativa, a associação é extremamente fraca.  
Isso significa que a idade não explica de forma relevante o ticket médio e, portanto, não deve ser utilizada de forma isolada como critério para segmentação de público-alvo.  

---

### Hipótese para problemática 3 —  

**Hipótese de Pesquisa**  
- H₀: A participação de lentes de grau nas vendas **é a mesma** entre estados/cidades.  
- H₁: A participação de lentes de grau **varia** por estado/cidade.  

**Two-tailed test:**   O que se avalia é se as proporções são **iguais ou diferentes** entre regiões, sem supor previamente se alguma é maior ou menor.  
  ➝ Como a variação pode ser em qualquer direção, o teste é bilateral.  

$$
H_{0}: p_{Estado1} = p_{Estado2} = \dots = p_{Estadon}
$$

$$
H_{1}: \exists \; i,j \;\; | \;\; p_{i} \neq p_{j}
$$

**Significados:**
- $p_{Estado k}$: proporção de vendas de lentes de grau no estado/cidade $k$.  
- $i, j$: índices representando diferentes estados ou cidades.  
- $H_{0}$: as proporções são iguais em todos os estados/cidades.  
- $H_{1}$: pelo menos uma proporção difere de outra.  

**Justificativa**  
Analisar se o impacto das Óticas Chilli Beans é o mesmo entre as diferentes localidades. Dessa forma, verificar se a participação das vendas de lentes de grau difere entre localidades (UF/cidade) para orientar decisões de sortimento, estoque e campanhas regionais.

**Evidência**  

<div align="center">
<sub>Figura X - Output hipótese 3

![Hipótese 3](../assets/secao-4-2-3/hypothesis3.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div> 

**Interpretação**  

O teste qui-quadrado indicou diferença significativa na distribuição das vendas entre as localidades (p < 0.05).
Isso nos leva a rejeitar H₀ e aceitar H₁: a participação de lentes de grau nas vendas varia de acordo com o estado/cidade.
Observamos que algumas localidades apresentam maior proporção de vendas de lentes de grau, enquanto outras concentram-se mais em óculos solares, sugerindo que o comportamento de consumo não é uniforme e que há necessidade de estratégias regionais de sortimento e marketing.

---

### Conclusão Geral  




### <a name="c4.3"></a>4.3. Preparação dos Dados e Modelagem

```
Caso seu projeto seja Modelo Supervisionado, apresentar: 
a) Organização dos dados (conjunto de treinamento, validação e testes)
b) Modelagem para o problema (proposta de features com a explicação completa da linha de raciocínio).
c) Métricas relacionadas ao modelo (pelo menos 3).
d) Apresentar o primeiro modelo candidato, e uma discussão sobre os resultados deste modelo (discussão sobre as métricas para esse modelo candidato).

Caso seu projeto seja Modelo Não-Supervisionado, apresentar:
a) Modelagem para o problema (proposta de features com a explicação completa da linha de raciocínio).
b) Primeiro modelo candidato para o problema.
c) Justificativa para a definição do K do modelo.
d) Escolha de um tipo de sistema de recomendação e a justificativa para essa escolha.

Remova este bloco ao final
```

### <a name="c4.4"></a>4.4. Comparação de Modelos

```
- Descrever e justificar a escolha da métrica de avaliação dos modelos com base no que é mais importante para o problema ao 
  se medir a qualidade desses modelos;
- Descrever ao menos três modelos candidatos, seus respectivos algoritmos, seus tunings de hiperparâmetros e suas métricas 
  alcançadas;

Remova este bloco ao final
```

### <a name="c4.5"></a>4.5.Avaliação

```
- Descreva a solução final de modelo preditivo e justifique a escolha. Alinhe sua justificativa com a Seção 4.1, resgatando o entendimento 
  do negócio e das personas, explicando de que formas seu modelo atende os requisitos e definições. 
- Descreva também um plano de contingência para os casos em que o modelo falhar em suas predições.
- Além disso, discuta sobre a explicabilidade do modelo (se aplicável) e realize a verificação de aceitação ou refutação das hipóteses.
- Se aplicável, utilize equações, tabelas e gráficos de visualização de dados para melhor ilustrar seus argumentos. 

Remova este bloco ao final
```

## <a name="c5"></a>5. Conclusões e Recomendações

```
Escreva, de forma resumida, sobre os principais resultados do seu projeto e faça recomendações formais ao seu parceiro de negócios em relação ao uso desse modelo. Você pode aproveitar este espaço para comentar sobre possíveis materiais extras, como um manual de usuário mais detalhado na seção “Anexos”. Não se esqueça também das pessoas que serão potencialmente afetadas pelas decisões do modelo preditivo e elabore recomendações que ajudem seu parceiro a tratá-las de maneira estratégica e ética. 

Remova este bloco ao final
```

## <a name="c6"></a>6. Referências

```
Incluir as principais referências de seu projeto, para que seu parceiro possa consultar caso ele se interessar em aprofundar. Não se esqueça de formatar as referências conforme a ABNT.

Remova este bloco ao final
```

## <a name="attachments"></a>Anexos

### Fundamentação Teórica e Formulação de Hipóteses

&ensp; A verificação da normalidade dos dados constitui etapa fundamental na análise estatística, uma vez que determina a escolha entre métodos paramétricos e não-paramétricos nas análises subsequentes. Para este estudo, formulou-se a seguinte afirmação a ser testada: as variáveis quantitativas do conjunto de dados seguem uma distribuição normal.

&ensp; As hipóteses estatísticas foram estabelecidas conforme a estrutura clássica de testes de hipóteses. A hipótese nula (H₀) postula que os dados seguem uma distribuição normal, enquanto a hipótese alternativa (H₁) estabelece que os dados não seguem uma distribuição normal. Esta formulação permite testar formalmente se cada variável possui distribuição normal, sendo fundamental para a escolha adequada de métodos estatísticos nas etapas posteriores da análise.

### Nível de Significância e Critérios de Decisão

&ensp; O nível de significância adotado foi α = 0,05 (5%), valor amplamente aceito na literatura científica por representar um equilíbrio adequado entre o risco de erro tipo I e o poder do teste. A relação estabelecida com o valor-p segue o critério padrão de decisão: quando p-valor ≤ 0,05, rejeita-se H₀, indicando evidência estatística de não-normalidade; quando p-valor > 0,05, não se rejeita H₀, sugerindo ausência de evidência suficiente contra a normalidade dos dados.

```
Utilize esta seção para anexar materiais como manuais de usuário, documentos complementares que ficaram grandes e não couberam no corpo do texto etc.

Remova este bloco ao final
```
