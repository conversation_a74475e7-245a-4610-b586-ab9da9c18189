{"cells": [{"cell_type": "markdown", "id": "b7b90ce4", "metadata": {}, "source": ["# Análise de Vendas por Tipo de PDV e Subgrupo de Produtos\n", "\n", "Este notebook realiza uma exploração das vendas de óticas, estruturando e visualizando métricas por tipo de ponto de venda (PDV) e por subgrupo de produtos.\n", "\n", "## 1. Carregamento e Preparação dos Dados\n", "\n", "- **Importação do módulo de filtragem**: Utiliza o módulo `data_filtering.py` que centraliza todas as regras de negócio\n", "- **Aplicação automática dos filtros**: Todos os filtros são aplicados automaticamente através do módulo\n", "- **Filtragem específica por tipo de PDV**: Mantém apenas os tipos de interesse: `[\"LOJA DE RUA OTICO\", \"LOJA OTICO\", \"QUIOSQUE OTICO\"]`\n", "- **Resultado**: Dataset `df_oticas` limpo e focado nos tipos de PDV relevantes para análise\n", "\n", "## 2. Construção de Matrizes (Pivot Tables)\n", "\n", "Foram criadas duas visões agregadas principais:\n", "\n", "| Matriz | Descrição | Métrica | Função |\n", "|--------|-----------|---------|---------|\n", "| `pivot` | Ticket médio por Tipo de PDV vs Subgrupo de Produto | `Valor_Total` | `mean` |\n", "| `pivot_qty` | Volume total por Tipo de PDV vs Subgrupo de Produto | `Quantidade` | `sum` |\n", "\n", "## 3. Visual<PERSON><PERSON><PERSON><PERSON>\n", "\n", "### Heatmap 1: <PERSON><PERSON><PERSON>\n", "- **<PERSON><PERSON>**: <PERSON><PERSON><PERSON> de `Valor_Total` por combinação de Tipo de PDV e Subgrupo\n", "- **Paleta**: \"Purples\" para destacar valores de ticket médio\n", "- **Anotações**: Valores exibidos diretamente nas células (formato inteiro)\n", "- **Insight**: Permite identificar quais subgrupos têm maior valor médio em cada tipo de loja\n", "\n", "### Heatmap 2: Volume de Vendas  \n", "- **Dados**: Soma de `Quantidade` por combinação de Tipo de PDV e Subgrupo\n", "- **Paleta**: \"Greens\" para destacar volume físico de vendas\n", "- **Anotações**: Quantidades totais exibidas nas células\n", "- **Insight**: Mostra quais produtos têm maior giro em cada tipo de loja\n", "\n", "## 4. <PERSON><PERSON><PERSON><PERSON>\n", "\n", "### <PERSON><PERSON><PERSON><PERSON> (por Subgrupo)\n", "- **`product_mean`**: Ranking de subgrupos por ticket médio (top 5)\n", "- **`qty_totals`**: Ranking de subgrupos por volume total vendido (top 5)\n", "\n", "## 5. <PERSON><PERSON><PERSON> da Análise\n", "\n", "### Por Tipo de PDV:\n", "- **LOJA OTICO**: <PERSON><PERSON> e amplitude de produtos\n", "- **LOJA DE RUA OTICO**: Foco em produtos específicos com valores elevados  \n", "- **QUIOSQUE OTICO**: Portfólio mais enxuto com valores medianos\n", "\n", "### Por Subgrupo:\n", "- **Alto valor/baixo volume**: Produtos premium como PROGRESSIVA e MULTIFOCAIS\n", "- **Alto volume/menor valor**: Produtos de giro como VISAO SIMPLES e LICENCIADO\n", "- **Diferenciação por canal**: Cada tipo de PDV apresenta mix de produtos distinto"]}, {"cell_type": "code", "execution_count": null, "id": "a812b1be", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import sys\n", "import os\n", "\n", "# Adicionar o diretório pai ao path para importar o módulo de filtragem\n", "sys.path.append(os.path.dirname(os.path.abspath('.')))\n", "\n", "# Importar o módulo de filtragem\n", "from data_filtering import apply_business_filters\n", "\n", "# Carregar e filtrar os dados usando o módulo reutilizável\n", "df = apply_business_filters('../assets/dados.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "be780313", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1400x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "TOP 5 produtos por valor médio (geral, sem separar por tipo de loja):\n", "Dim_Produtos.Sub_Grupo\n", "MULTI            523.832562\n", "ACETATO          486.420029\n", "VISAO SIMPLES    430.217362\n", "METAL            409.827149\n", "CLASSICO         336.536439\n", "Name: Valor_Total, dtype: float64\n"]}], "source": ["# Filtrar apenas os tipos de PDV desejados\n", "tipos_pdv = [\"LOJA DE RUA OTICO\", \"LOJA OTICO\", \"QUIOSQUE OTICO\"]\n", "df_oticas = df[df['Dim_Lojas.Tipo_PDV'].isin(tipos_pdv)]\n", "\n", "# Criando uma tabela pivot: lojas x tipos de produtos com média das vendas\n", "pivot = df_oticas.pivot_table(\n", "    index='Dim_Lojas.Tipo_PDV',\n", "    columns='Dim_Produtos.Sub_Grupo',\n", "    values='Valor_Total',\n", "    aggfunc='mean',\n", "    fill_value=0\n", ")\n", "\n", "if pivot.size > 0:\n", "    # Plotando o heatmap\n", "    plt.figure(figsize=(14,8))\n", "    \n", "    # Annot para mostrar os valores\n", "    # fmt=\".0f\" para mostrar apenas valores inteiros\n", "    # cmap=\"Purples\" para usar uma paleta de roxo\n", "    # cbar_kws={'label': 'Valor Total (R$)'} para adicionar um label à barra de cores\n", "    sns.heatmap(pivot, annot=True, fmt=\".0f\", cmap=\"Purples\", cbar_kws={'label': 'Valor Total (R$)'})\n", "    \n", "    plt.title(\"Ticket Médio de Vendas por Tipo de Loja e Produto\")\n", "    plt.xlabel(\"Produto\")\n", "    plt.ylabel(\"Loja\")\n", "    plt.xticks(rotation=45, ha='right')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(f\"\\nTOP 5 produtos por valor médio (geral, sem separar por tipo de loja):\")\n", "    # Calculando a média diretamente dos dados originais, sem agregar por tipo de PDV\n", "    product_mean = df_oticas.groupby('Dim_Produtos.Sub_Grupo')['Valor_Total'].mean().sort_values(ascending=False)\n", "    print(product_mean.head())\n", "else:\n", "    print(\"ERRO: Tabela auxiliar vazia!\")"]}, {"cell_type": "code", "execution_count": 6, "id": "3e0a7462", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1400x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "TOP 5 produtos por quantidade total vendida (geral, sem separar por tipo de loja):\n", "Dim_Produtos.Sub_Grupo\n", "VISAO SIMPLES    730.0\n", "LICENCIADO       712.0\n", "CLASSICO         555.0\n", "MULTI            487.0\n", "METAL            451.0\n", "dtype: float64\n"]}], "source": ["# Opção 4: Quantidade total vendida (volume físico)\n", "pivot_qty = df_oticas.pivot_table(\n", "    index='Dim_Lojas.Tipo_PDV',\n", "    columns='Dim_Produtos.Sub_Grupo',\n", "    values='Quantidade',  # Usando quantidade em vez de valor\n", "    aggfunc='sum',\n", "    fill_value=0\n", ")\n", "\n", "plt.figure(figsize=(14,8))\n", "sns.heatmap(pivot_qty, annot=True, fmt=\".0f\", cmap=\"Greens\", cbar_kws={'label': 'Quantidade Total'})\n", "plt.title(\"Volume de Produtos Vendidos por Tipo de Loja e Produto\")\n", "plt.xlabel(\"Produto\")\n", "plt.ylabel(\"Loja\")\n", "plt.xticks(rotation=45, ha='right')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\nTOP 5 produtos por quantidade total vendida (geral, sem separar por tipo de loja):\")\n", "qty_totals = pivot_qty.sum(axis=0).sort_values(ascending=False)\n", "print(qty_totals.head())"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}