%load_ext autoreload
%autoreload 2

import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np
import sys
import os

# Adicionar o diretório pai ao path para importar o módulo de filtragem
sys.path.append(os.path.dirname(os.path.abspath('.')))

# Importar o módulo de filtragem
from data_filtering import apply_business_filters

# Carregar e filtrar os dados usando o módulo reutilizável
df = apply_business_filters('../assets/dados.csv')

# Criando a nova coluna de Regiões
df['Regiao'] = df['Dim_Lojas.REGIAO_CHILLI']

# Filtrar apenas os tipos de PDV desejados
df_oticas = df

# Criar a coluna de Ticket Médio (Valor_Total dividido pela Quantidade)
df_oticas["Ticket_Medio"] = df_oticas["Valor_Total"] / df_oticas["Quantidade"]

# Agrupar por Canal de Venda (CANAL_VENDA) e Região, calcular média do ticket médio
ticket_medio = (
    df_oticas.groupby(["Dim_Lojas.CANAL_VENDA", "Regiao"])["Ticket_Medio"]
    .mean()
    .unstack()
    .fillna(0)  # Preencher valores NaN com 0
)

# Configurações de estilo para os gráficos
plt.style.use('seaborn-v0_8-darkgrid')
plt.rcParams['figure.facecolor'] = 'white'
plt.rcParams['axes.facecolor'] = 'white'

# Plotar gráfico de barras com estilo melhorado
fig, ax = plt.subplots(figsize=(14, 8))

# Cores personalizadas para as regiões
cores_regioes = {
    'CENTRO-OESTE': '#FF6B6B',
    'NORDESTE': '#4ECDC4', 
    'NORTE': '#45B7D1',
    'SUDESTE': '#96CEB4',
    'SUL': '#FFEAA7'
}

# Plotar com cores personalizadas
ticket_medio.plot(kind="bar", ax=ax, color=[cores_regioes.get(col, '#95A5A6') for col in ticket_medio.columns])

# Melhorias no estilo
ax.set_title("Ticket Médio por Canal de Venda e Região", fontsize=18, fontweight='bold', pad=20)
ax.set_ylabel("Ticket Médio (R$)", fontsize=14, fontweight='bold')
ax.set_xlabel("Canal de Venda", fontsize=14, fontweight='bold')

# Configurar a legenda
ax.legend(title="Região", bbox_to_anchor=(1.05, 1), loc="upper left", 
          title_fontsize=12, fontsize=11, frameon=True, fancybox=True, shadow=True)

# Rotacionar labels do eixo x para melhor legibilidade
plt.setp(ax.get_xticklabels(), rotation=45, ha='right', fontsize=11)

# Adicionar grid mais sutil
ax.grid(True, alpha=0.3, linestyle='--')

# Formatar valores no eixo y como moeda brasileira
ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'R$ {x:,.0f}'))

# Adicionar valores nas barras
for container in ax.containers:
    ax.bar_label(container, fmt='R$ %.0f', rotation=90, fontsize=9, padding=3)

plt.tight_layout()
plt.show()

# Exibir informações sobre os dados processados
print(f"Canais de venda únicos após processamento: {sorted(df_oticas['Dim_Lojas.CANAL_VENDA'].unique())}")
print(f"Regiões disponíveis: {sorted(df_oticas['Regiao'].unique())}")
print(f"\nShape da matriz ticket_medio: {ticket_medio.shape}")

# --- Criando o Top 5 geral com melhor formatação ---
top5 = (
    ticket_medio.reset_index()  # tira MultiIndex (Canal e Região viram colunas)
    .melt(id_vars="Dim_Lojas.CANAL_VENDA", var_name="Regiao", value_name="Ticket_Medio")  # formato longo
    .dropna(subset=["Ticket_Medio"])  # remove NaN
    .query("Ticket_Medio > 0")  # remove valores zero
    .sort_values("Ticket_Medio", ascending=False)  # ordena do maior pro menor
    .head(5)  # pega os 5 primeiros
    .reset_index(drop=True)
)

print("TOP 5 TICKET MÉDIO POR CANAL DE VENDA E REGIÃO")
for i, row in top5.iterrows():
    posicao = i + 1
    canal = row['Dim_Lojas.CANAL_VENDA']
    regiao = row['Regiao']
    valor = row['Ticket_Medio']
    print(f"{posicao}º lugar: {canal} - {regiao}: R$ {valor:,.0f}")

# Análise adicional por canal
print("\nTICKET MÉDIO POR CANAL DE VENDA (MÉDIA GERAL)")
ticket_por_canal = df_oticas.groupby('Dim_Lojas.CANAL_VENDA')['Ticket_Medio'].mean().sort_values(ascending=False)
for canal, valor in ticket_por_canal.items():
    print(f"{canal}: R$ {valor:,.0f}")

# Análise adicional por região
print("\nTICKET MÉDIO POR REGIÃO (MÉDIA GERAL)")
ticket_por_regiao = df_oticas.groupby('Regiao')['Ticket_Medio'].mean().sort_values(ascending=False)
for regiao, valor in ticket_por_regiao.items():
    print(f"{regiao}: R$ {valor:,.0f}")

import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap

# Criar colormap personalizado mais adequado para ticket médio (azul → verde → amarelo → vermelho)
cores_personalizadas = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D']
cmap = LinearSegmentedColormap.from_list("ticket_medio", cores_personalizadas, N=256)

# Configurar o estilo
plt.style.use('seaborn-v0_8-white')

# Plotar gráfico de heatmap melhorado
fig, ax = plt.subplots(figsize=(12, 8))

# Criar o heatmap com melhorias visuais
sns.heatmap(ticket_medio, 
            annot=True, 
            fmt='.0f',  # Formato sem casas decimais para valores em reais
            cmap=cmap, 
            cbar_kws={
                'label': 'Ticket Médio (R$)',
                'shrink': 0.8,
                'aspect': 20
            },
            linewidths=1,
            linecolor='white',
            square=False,
            annot_kws={
                'fontsize': 11,
                'fontweight': 'bold'
            },
            ax=ax)

# Melhorias no título e labels
ax.set_title("Heatmap: Ticket Médio por Canal de Venda e Região", 
             fontsize=18, fontweight='bold', pad=25)
ax.set_xlabel("Região", fontsize=14, fontweight='bold')
ax.set_ylabel("Canal de Venda", fontsize=14, fontweight='bold')

# Melhorar a formatação dos labels
plt.setp(ax.get_xticklabels(), rotation=45, ha='right', fontsize=12)
plt.setp(ax.get_yticklabels(), rotation=0, fontsize=12)

# Adicionar borda ao redor do heatmap
for spine in ax.spines.values():
    spine.set_visible(True)
    spine.set_linewidth(2)
    spine.set_edgecolor('black')

plt.tight_layout()
plt.show()