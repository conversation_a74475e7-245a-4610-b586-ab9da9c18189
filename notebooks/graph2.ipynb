{"cells": [{"cell_type": "markdown", "id": "bf1c8597", "metadata": {}, "source": ["# Análise de Compras por Faixa Etária e Grupo de Produto\n", "\n", "Este notebook calcula a idade dos clientes a partir da data de nascimento, classifica-os em faixas etárias e, em seguida, gera uma visualização que mostra quais grupos de produtos são mais consumidos em cada faixa de idade.\n", "\n", "O resultado final é um gráfico de barras empilhadas, que permite analisar a distribuição de compras de acordo com a geração do cliente."]}, {"cell_type": "markdown", "id": "16396a59", "metadata": {}, "source": ["## 1. Preparação dos Dados\n", "\n", "- Conversão da coluna `Dim_Cliente.Data_Nascimento` para o formato datetime.  \n", "- <PERSON><PERSON><PERSON><PERSON><PERSON> da idade em anos, usando a diferença entre a data atual e a data de nascimento.  \n", "- Exclusão de clientes com menos de 18 anos.  \n", "\n", "## 2. Defin<PERSON><PERSON> das Faixas Etárias\n", "\n", "As idades foram agrupadas nos seguintes intervalos:\n", "\n", "- 18–29  \n", "- 30–39  \n", "- 40–49  \n", "- 50–59  \n", "- 60–69  \n", "- 70–79  \n", "- 80+  \n", "\n", "<PERSON><PERSON> é feito com `pd.cut`, criando uma coluna categórica chamada **faixa_etaria**.\n", "\n", "## 3. Agrupamento por Faixa e Produto\n", "\n", "Os dados foram agregados com `groupby`, contabilizando a quantidade de registros por **faixa_etaria × Dim_Produtos.Grupo_Produto**.  \n", "Em seguida, a tabela foi transformada em formato matricial (`pivot`) para facilitar a construção do gráfico.\n", "\n", "## 4. Visualização\n", "\n", "- Gráfico de barras empilhadas (`stacked=True`) para mostrar a composição de cada faixa etária em termos de grupo de produto.  \n", "- Paleta de cores `tab20c` para distinguir cada categoria de produto.  \n", "- Legenda posicionada fora da área do gráfico, organizada em **3 colunas**, para evitar sobreposição.  \n", "\n", "Esse gráfico revela quais **grupos de produtos** são mais consumidos em cada faixa etária, permitindo insights sobre preferências de compra por geração.\n"]}, {"cell_type": "code", "execution_count": null, "id": "f05f5163", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import sys\n", "import os\n", "\n", "# Adicionar o diretório pai ao path para importar o módulo de filtragem\n", "sys.path.append(os.path.dirname(os.path.abspath('.')))\n", "\n", "# Importar o módulo de filtragem\n", "from data_filtering import apply_business_filters\n", "\n", "# Carregar e filtrar os dados usando o módulo reutilizável\n", "df = apply_business_filters('../assets/dados.csv')"]}, {"cell_type": "code", "execution_count": 2, "id": "a3664c64", "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "# Converter coluna de data de nascimento para datetime (se já não estiver)\n", "df['Dim_Cliente.Data_Nascimento'] = pd.to_datetime(df['Dim_Cliente.Data_Nascimento'])\n", "\n", "# Data de referência (hoje) usada para calcular idades\n", "hoje = pd.to_datetime(datetime.today().date())\n", "\n", "# Selecionar apenas colunas relevantes para este cálculo: grupo de produto e data de nascimento\n", "df_new = df[['Dim_Produtos.Grupo_Produto', 'Dim_Cliente.Data_Nascimento']].copy()\n", "\n", "# Calcular a idade inteira em anos (aproximação simples usando dias // 365)\n", "df_new['idade'] = (hoje - df_new['Dim_Cliente.Data_Nascimento']).dt.days // 365\n", "\n", "# Excluir registros de menores de 18 anos\n", "df_new = df_new[df_new['idade'] > 17]"]}, {"cell_type": "code", "execution_count": 3, "id": "05010078", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/dq/83qxsj710bbgjhs2_rywgz900000gn/T/ipykernel_66744/3286330854.py:10: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  agrupado = df_new.groupby(['faixa_etaria', 'Dim_Produtos.Grupo_Produto']).size().reset_index(name='qtd')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>faixa_etaria</th>\n", "      <th>Dim_Produtos.Grupo_Produto</th>\n", "      <th>qtd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>18-29</td>\n", "      <td>ACESSORIOS</td>\n", "      <td>1208</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>18-29</td>\n", "      <td>ASSIST TEC REL REVENDA</td>\n", "      <td>23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>18-29</td>\n", "      <td>BAG</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>18-29</td>\n", "      <td>GRAU</td>\n", "      <td>444</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>18-29</td>\n", "      <td>LENTE CONTATO GRAU</td>\n", "      <td>40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>18-29</td>\n", "      <td>LENTES VISTA</td>\n", "      <td>216</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>18-29</td>\n", "      <td>MATERIAIS CONSUMIVEIS</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>18-29</td>\n", "      <td>MATERIAIS VENDAVEIS</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>18-29</td>\n", "      <td>OCULOS</td>\n", "      <td>2027</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>18-29</td>\n", "      <td>Outros</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>18-29</td>\n", "      <td>RELOGIOS</td>\n", "      <td>132</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>30-39</td>\n", "      <td>ACESSORIOS</td>\n", "      <td>1795</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>30-39</td>\n", "      <td>ASSIST TEC REL REVENDA</td>\n", "      <td>39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>30-39</td>\n", "      <td>BAG</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>30-39</td>\n", "      <td>GRAU</td>\n", "      <td>567</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>30-39</td>\n", "      <td>LENTE CONTATO GRAU</td>\n", "      <td>41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>30-39</td>\n", "      <td>LENTES VISTA</td>\n", "      <td>261</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>30-39</td>\n", "      <td>MATERIAIS CONSUMIVEIS</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>30-39</td>\n", "      <td>MATERIAIS VENDAVEIS</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>30-39</td>\n", "      <td>OCULOS</td>\n", "      <td>4224</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   faixa_etaria Dim_Produtos.Grupo_Produto   qtd\n", "0         18-29                 ACESSORIOS  1208\n", "1         18-29     ASSIST TEC REL REVENDA    23\n", "2         18-29                        BAG     7\n", "3         18-29                       GRAU   444\n", "4         18-29         LENTE CONTATO GRAU    40\n", "5         18-29               LENTES VISTA   216\n", "6         18-29      <PERSON><PERSON>RI<PERSON>IS CONSUMIVEIS     4\n", "7         18-29        MATERIAIS VENDAVEIS     3\n", "8         18-29                     OCULOS  2027\n", "9         18-29                     Outros     3\n", "10        18-29                   RELOGIOS   132\n", "11        30-39                 ACESSORIOS  1795\n", "12        30-39     ASSIST TEC REL REVENDA    39\n", "13        30-39                        BAG    12\n", "14        30-39                       GRAU   567\n", "15        30-39         LENTE CONTATO GRAU    41\n", "16        30-39               LENTES VISTA   261\n", "17        30-39      MATERIAIS CONSUMIVEIS     7\n", "18        30-39        MATERIAIS VENDAVEIS     0\n", "19        30-39                     OCULOS  4224"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Definir as faixas de idade (bins) e os rótulos correspondentes\n", "bins = [18, 29, 39, 49, 59, 69, 79, 106]  # limites inferiores e superiores\n", "labels = ['18-29', '30-39', '40-49', '50-59', '60-69', '70-79', '80+']\n", "\n", "# Criar coluna com a faixa etária usando pd.cut\n", "# right=True significa que o limite direito é incluído (ex.: 29 entra na primeira faixa)\n", "df_new['faixa_etaria'] = pd.cut(df_new['idade'], bins=bins, labels=labels, right=True)\n", "\n", "# Agrupar por faixa etária e grupo de produto, contando ocorrências\n", "agrupado = df_new.groupby(['faixa_etaria', 'Dim_Produtos.Grupo_Produto']).size().reset_index(name='qtd')\n", "agrupado.head(20)  # <PERSON><PERSON> as primeiras linhas do agrupamento para inspeção"]}, {"cell_type": "markdown", "id": "d923e90f", "metadata": {}, "source": ["## Interpretação do Gráfico\n", "\n", "- <PERSON>ada barra representa uma faixa etária.  \n", "- A altura total da barra corresponde ao número total de compras naquela faixa.  \n", "- As cores empilhadas mostram a contribuição de cada **grupo de produto**.  \n", "\n", "<PERSON><PERSON><PERSON>, é possível identificar:\n", "- Quais faixas etárias têm maior volume de compras.  \n", "- Quais produtos dominam em cada faixa (ex.: óculos entre jovens adultos, multifocais entre faixas acima de 40 anos).  \n", "- Diferenças de portfólio entre gerações.  "]}, {"cell_type": "code", "execution_count": 4, "id": "433071f3", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Figure size 1400x700 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1400x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Pivotar o dataframe 'agrupado' para ter faixas etárias nas linhas e grupos de produto nas colunas\n", "pivot = agrupado.pivot(index=\"faixa_etaria\", \n", "                       columns=\"Dim_Produtos.Grupo_Produto\", \n", "                       values=\"qtd\").fillna(0)\n", "\n", "# Escolher uma paleta de cores com base no número de colunas (grupos de produto)\n", "colors = sns.color_palette(\"tab20c\", n_colors=len(pivot.columns))\n", "\n", "# Criar figura e plotar um gráfico de barras empilhadas\n", "plt.figure(figsize=(14,7))\n", "pivot.plot(\n", "    kind=\"bar\", \n", "    stacked=True, \n", "    figsize=(14,7), \n", "    color=colors, \n", "    linewidth=0.5,\n", ")\n", "\n", "# Ajustar títulos e rótulos para melhor leitura\n", "plt.title(\"Distribuição de Compras por Faixa Etária e Grupo de Produto\", fontsize=14, weight=\"bold\")\n", "plt.xlabel(\"Faixa Etária\", fontsize=12)\n", "plt.ylabel(\"Quantidade\", fontsize=12)\n", "plt.xticks(rotation=0)\n", "\n", "# Posicionar a legenda fora do gráfico para evitar sobreposição\n", "plt.legend(title=\"Grupo de Produto\", bbox_to_anchor=(1.05, 1), loc=\"upper left\", ncol=3)\n", "\n", "plt.tight_layout()\n", "plt.show()  # Exibir o gráfico empilhado"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}