{"cells": [{"cell_type": "code", "execution_count": null, "id": "d698cee2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Documents\\mod 3\\2025-2A-T17-IN03-G02\\data_filtering.py:33: DtypeWarning: Columns (46) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(csv_path)\n"]}], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import sys\n", "import os\n", "\n", "# Adicionar o diretório pai ao path para importar o módulo de filtragem\n", "sys.path.append(os.path.dirname(os.path.abspath('.')))\n", "\n", "# Importar o módulo de filtragem\n", "from data_filtering import apply_business_filters\n", "\n", "# Carregar e filtrar os dados usando o módulo reutilizável\n", "\n", "df = apply_business_filters('../assets/dados.csv')"]}, {"cell_type": "code", "execution_count": 22, "id": "df4b28b3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "\n", "df = df[df['Natureza_Operacao'] != 'DEVOLUCAO DE MERCADORIA']\n", "df = df[df['Preco_Varejo'] > 1]\n", "df = df[df['Total_Preco_Varejo'] > 1]\n", "\n", "df['diferenca'] = df['Quantidade'] * df['Preco_Varejo'] - df['DESCONTO_CALCULADO'] - df['Valor_Total']\n", "df = df[df['diferenca'] < 1]\n", "\n", "df['Dim_Cliente.Data_Nascimento'] = pd.to_datetime(df['Dim_Cliente.Data_Nascimento'], errors='coerce')\n", "df['ID_Date'] = pd.to_datetime(df['ID_Date'], errors='coerce')\n", "df['Dim_Cliente.Data_Cadastro'] = pd.to_datetime(df['Dim_Cliente.Data_Cadastro'], errors='coerce')\n", "\n", "df['idade'] = (df['ID_Date'] - df['Dim_Cliente.Data_Nascimento']).dt.days / 365.25\n", "df = df[(df['idade'] >= 10) & (df['idade'] <= 100)]\n", "\n", "df['idade_cadastro'] = (df['Dim_Cliente.Data_Cadastro'] - df['Dim_Cliente.Data_Nascimento']).dt.days / 365.25\n", "df = df[df['idade_cadastro'] >= 18]\n", "\n", "df = df.drop_duplicates(subset=['DOC_UNICO', 'ID_Produto', 'ID_Cliente'])\n", "\n", "df['check_total'] = df['Quantidade'] * df['Preco_Varejo'] - df['DESCONTO_CALCULADO']\n", "df = df[np.isclose(df['check_total'], df['Valor_Total'], atol=2)]\n", "\n", "df = df[df['Dim_Lojas.Tipo_PDV'].notnull()]\n", "df = df[df['Dim_Produtos.Nome'].notnull()]\n", "df = df[df['Dim_Produtos.Grupo_Produto'].notnull()]\n", "\n", "df['Dim_Lojas.Tipo_PDV'] = df['Dim_Lojas.Tipo_PDV'].astype(str).str.strip()\n", "df['Dim_Produtos.Sub_Grupo'] = df['Dim_Produtos.Sub_Grupo'].astype(str).str.strip()"]}, {"cell_type": "code", "execution_count": 23, "id": "f862c136", "metadata": {}, "outputs": [], "source": ["def estatisticas_numericas(coluna, preencher_na=False):\n", "    \"\"\"\n", "    Gera estatísticas descritivas para uma coluna numérica do dataframe global df.\n", "\n", "    Parâmetros:\n", "    - coluna: nome da coluna no dataframe df.\n", "    - preencher_na: se <PERSON>, preenche valores nulos com a média da coluna.\n", "\n", "    Retorna:\n", "    - dict com média, mediana, moda, desvio padr<PERSON>, contagem de valores, soma, mínimo e máximo.\n", "    \"\"\"\n", "    if coluna not in df.columns:\n", "        raise ValueError(f\"A coluna '{coluna}' não existe no dataframe.\")\n", "    \n", "    serie = df[coluna]\n", "\n", "    if not pd.api.types.is_numeric_dtype(serie):\n", "        raise TypeError(f\"A coluna '{coluna}' não é numérica.\")\n", "\n", "    media = float(serie.mean())\n", "    mediana = float(serie.median())\n", "    moda = float(serie.mode().iloc[0]) if not serie.mode().empty else None\n", "    desvio_padrao = float(serie.std())\n", "    contagem_valores = float(serie.count())\n", "    soma_total = float(serie.sum())\n", "    minimo = float(serie.min())\n", "    maximo = float(serie.max())\n", "\n", "    if preencher_na:\n", "        df[coluna].fillna(media, inplace=True)\n", "\n", "    return {\n", "        \"coluna\": coluna,\n", "        \"media\": media,\n", "        \"mediana\": mediana,\n", "        \"moda\": moda,\n", "        \"desvio_padrao\": desvio_padrao,\n", "        \"soma_total\": soma_total,\n", "        \"minimo\": minimo,\n", "        \"maximo\": maximo,\n", "        \"contagem_valores\": contagem_valores\n", "    }"]}, {"cell_type": "code", "execution_count": 25, "id": "8bad2527", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON><PERSON><PERSON> da coluna Preco_Custo:\n", "{\n", "    \"coluna\": \"Preco_Custo\",\n", "    \"media\": 76.36766374475961,\n", "    \"mediana\": 73.26,\n", "    \"moda\": 69.21,\n", "    \"desvio_padrao\": 75.19361114185793,\n", "    \"soma_total\": 1402644.88,\n", "    \"minimo\": 1.8,\n", "    \"maximo\": 1379.16,\n", "    \"contagem_valores\": 18367.0\n", "}\n", "\n", "<PERSON><PERSON><PERSON><PERSON> da coluna DESCONTO_CALCULADO:\n", "{\n", "    \"coluna\": \"DESCONTO_CALCULADO\",\n", "    \"media\": 8.045381254423695,\n", "    \"mediana\": 0.0,\n", "    \"moda\": 0.0,\n", "    \"desvio_padrao\": 56.362876160594965,\n", "    \"soma_total\": 147769.51750000002,\n", "    \"minimo\": -81.7357,\n", "    \"maximo\": 3018.0076,\n", "    \"contagem_valores\": 18367.0\n", "}\n", "\n", "<PERSON><PERSON><PERSON><PERSON> da coluna Quantidade:\n", "{\n", "    \"coluna\": \"Quantidade\",\n", "    \"media\": 1.09021614852725,\n", "    \"mediana\": 1.0,\n", "    \"moda\": 1.0,\n", "    \"desvio_padrao\": 0.3259705051406093,\n", "    \"soma_total\": 20024.0,\n", "    \"minimo\": 1.0,\n", "    \"maximo\": 12.0,\n", "    \"contagem_valores\": 18367.0\n", "}\n", "\n", "<PERSON><PERSON><PERSON><PERSON> da coluna <PERSON>or_Total:\n", "{\n", "    \"coluna\": \"Valor_Total\",\n", "    \"media\": 305.62926348886583,\n", "    \"mediana\": 322.1389,\n", "    \"moda\": 399.98,\n", "    \"desvio_padrao\": 316.62076520376064,\n", "    \"soma_total\": 5613492.682499999,\n", "    \"minimo\": 0.01,\n", "    \"maximo\": 11057.9697,\n", "    \"contagem_valores\": 18367.0\n", "}\n", "\n"]}], "source": ["import json\n", "\n", "colunas_analise = ['Preco_Custo', 'DESCONTO_CALCULADO', 'Quantidade', 'Valor_Total']\n", "\n", "# Exemplo de uso da função\n", "# Substitua 'nome_da_coluna' pelo nome de uma coluna numérica do seu dataframe\n", "for coluna in colunas_analise:\n", "    resultado = estatisticas_numericas(coluna)\n", "    print(f\"<PERSON><PERSON><PERSON><PERSON> da coluna {coluna}:\")\n", "    print(json.dumps(resultado, ensure_ascii=False, indent=4))\n", "    print()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}