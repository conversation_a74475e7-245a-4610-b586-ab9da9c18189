"""
Módulo de pré-processamento de dados para análise de vendas da Chilli Beans.

Este módulo contém todas as funções necessárias para pré-processamento e limpeza dos dados.
O objetivo é centralizar as transformações de dados em um local reutilizável e modular.

Principais transformações aplicadas:
1. Tratamento de valores ausentes (missing values)
2. Detecção e tratamento de outliers usando IQR
3. Normalização Min-Max (0-1) e Padronização Z-Score
4. Codificação de variáveis categóricas (One-Hot e Label Encoding)
5. Validação e documentação de todas as transformações

Data: 2025-08-21
"""

import pandas as pd
import numpy as np
import warnings
from typing import Dict, List, Tuple, Any, TYPE_CHECKING

# Integração com sklearn Pipeline
try:
    from sklearn.base import BaseEstimator, TransformerMixin
    from sklearn.pipeline import Pipeline as SkPipeline
except Exception:  # sklearn pode não estar instalado ainda
    BaseEstimator = object  # type: ignore
    class TransformerMixin:  # type: ignore
        pass
    SkPipeline = None  # type: ignore

if TYPE_CHECKING:  # para tipagem estática opcional
    from sklearn.pipeline import Pipeline  # pragma: no cover

warnings.filterwarnings('ignore')

def preprocess_data(csv_path: str, verbose: bool = False) -> Tuple[pd.DataFrame, Dict[str, List[str]]]:
    """Aplica o pipeline de pré-processamento aos dados de vendas usando sklearn Pipeline."""

    from data_filtering import apply_business_filters  # import local para evitar ciclos

    df = apply_business_filters(csv_path, verbose=False)
    if verbose:
        print(f"Dataset carregado: {df.shape[0]:,} registros, {df.shape[1]} colunas")

    # Construir Pipeline moderno
    pipeline = _build_preprocessing_pipeline(verbose=verbose)
    df_processed = pipeline.fit_transform(df)

    # Coletar ações dos steps (cada transformer registra .actions_)
    acoes_documentadas: Dict[str, List[str]] = {
        'missing_values': getattr(pipeline.named_steps['missing'], 'actions_', []),
        'outliers': getattr(pipeline.named_steps['outliers'], 'actions_', []),
        'normalization': getattr(pipeline.named_steps['scaling'], 'actions_', []),
        'encoding': getattr(pipeline.named_steps['encoding'], 'actions_', []),
    }

    if verbose:
        print(f"Dataset final: {df_processed.shape[0]:,} registros, {df_processed.shape[1]} colunas")
        print(f"Valores ausentes restantes: {df_processed.isnull().sum().sum()}")

    return df_processed, acoes_documentadas

class MissingValueTransformer(BaseEstimator, TransformerMixin):
    """Trata valores ausentes replicando a lógica original.

    - Remove colunas com >50% de NaNs
    - Imputa categóricas com moda
    - Imputa numéricas com mediana
    Armazena ações em self.actions_.
    """
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.columns_to_drop_: List[str] = []
        self.modes_: Dict[str, object] = {}
        self.medians_: Dict[str, float] = {}
        self.actions_: List[str] = []

    def fit(self, X: pd.DataFrame, y=None):
        df = X
        missing_data = df.isnull().sum()
        missing_cols = missing_data[missing_data > 0]
        for col in missing_cols.index:
            missing_percent = (missing_data[col] / len(df)) * 100
            if missing_percent > 50:
                self.columns_to_drop_.append(col)
                self.actions_.append(f"{col}: Remoção da coluna (>50% ausentes)")
            elif df[col].dtype == 'object':
                moda = df[col].mode()[0] if not df[col].mode().empty else 'Não informado'
                self.modes_[col] = moda
                self.actions_.append(f"{col}: Imputação com moda '{moda}'")
            else:
                mediana = float(df[col].median())
                self.medians_[col] = mediana
                self.actions_.append(f"{col}: Imputação com mediana {mediana:.2f}")
        if len(missing_cols) == 0:
            self.actions_.append("Nenhuma ação necessária - dataset sem valores ausentes")
        return self

    def transform(self, X: pd.DataFrame):
        df = X.copy()
        if self.columns_to_drop_:
            df = df.drop(columns=self.columns_to_drop_, errors='ignore')
        for col, moda in self.modes_.items():
            if col in df.columns:
                df[col] = df[col].fillna(moda)
        for col, mediana in self.medians_.items():
            if col in df.columns:
                df[col] = df[col].fillna(mediana)
        return df

class OutlierTransformer(BaseEstimator, TransformerMixin):
    """Detecta e trata outliers segundo a lógica original.

    Mantém possibilidade de remover linhas (shape pode mudar), o que é aceito
    pois este pipeline é usado apenas para preparação pré-modelagem neste contexto.
    """
    def __init__(self, numeric_columns: List[str] | None = None, verbose: bool = False):
        self.numeric_columns = numeric_columns
        self.verbose = verbose
        self.actions_: List[str] = []
        self.thresholds_: Dict[str, Tuple[float, float]] = {}

    def _detect_iqr(self, s: pd.Series):
        Q1, Q3 = s.quantile(0.25), s.quantile(0.75)
        IQR = Q3 - Q1
        lower, upper = Q1 - 1.5 * IQR, Q3 + 1.5 * IQR
        return lower, upper

    def fit(self, X: pd.DataFrame, y=None):
        df = X
        cols = self.numeric_columns or ['Quantidade', 'Preco_Custo', 'Valor_Total', 'Preco_Varejo', 'Frete', 'Desconto']
        self.cols_ = [c for c in cols if c in df.columns]
        # Guardar limites max e min para logging apenas
        for col in self.cols_:
            lower, upper = self._detect_iqr(df[col])
            self.thresholds_[col] = (lower, upper)
        return self

    def transform(self, X: pd.DataFrame):
        df = X.copy()
        for col in self.cols_:
            lower, upper = self.thresholds_[col]
            outliers_mask = (df[col] < lower) | (df[col] > upper)
            percentual = outliers_mask.mean() * 100
            if percentual > 10:
                df[col] = np.where(df[col] < lower, lower, df[col])
                df[col] = np.where(df[col] > upper, upper, df[col])
                self.actions_.append(f"{col}: Cap truncation nos limites [{lower:.2f}, {upper:.2f}] ({percentual:.1f}% outliers)")
            elif percentual > 5:
                p5 = df[col].quantile(0.05)
                p95 = df[col].quantile(0.95)
                df[col] = np.where(df[col] < p5, p5, df[col])
                df[col] = np.where(df[col] > p95, p95, df[col])
                self.actions_.append(f"{col}: Substituição por percentis 5% ({p5:.2f}) e 95% ({p95:.2f}) ({percentual:.1f}% outliers)")
            elif percentual > 1:
                before = len(df)
                df = df[~outliers_mask]
                removed = before - len(df)
                self.actions_.append(f"{col}: Remoção de {removed} registros ({percentual:.1f}% outliers)")
            else:
                self.actions_.append(f"{col}: Mantidos ({percentual:.1f}% outliers - impacto negligível)")
        return df

class ScalingTransformer(BaseEstimator, TransformerMixin):
    """Aplica normalização Min-Max e padronização Z-Score replicando as colunas *_normalized e *_standardized."""
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.actions_: List[str] = []
        self.stats_: Dict[str, Dict[str, float]] = {}

    def fit(self, X: pd.DataFrame, y=None):
        cols = ['Quantidade', 'Preco_Custo', 'Valor_Total', 'Preco_Varejo', 'Frete', 'Desconto']
        self.cols_ = [c for c in cols if c in X.columns]
        for col in self.cols_:
            s = X[col]
            self.stats_[col] = {
                'min': float(s.min()),
                'max': float(s.max()),
                'mean': float(s.mean()),
                'std': float(s.std())
            }
        return self

    def transform(self, X: pd.DataFrame):
        df = X.copy()
        for col in self.cols_:
            st = self.stats_[col]
            if st['max'] != st['min']:
                df[f'{col}_normalized'] = (df[col] - st['min']) / (st['max'] - st['min'])
                self.actions_.append(f"{col}: Normalização Min-Max (0-1): [{st['min']:.2f}, {st['max']:.2f}] → [0, 1]")
            else:
                df[f'{col}_normalized'] = 0
                self.actions_.append(f"{col}: Normalização Min-Max: valores constantes → 0")
            if st['std'] != 0:
                df[f'{col}_standardized'] = (df[col] - st['mean']) / st['std']
                self.actions_.append(f"{col}: Padronização Z-Score: μ={st['mean']:.2f}, σ={st['std']:.2f} → μ≈0, σ≈1")
            else:
                df[f'{col}_standardized'] = 0
                self.actions_.append(f"{col}: Padronização Z-Score: desvio zero → 0")
        return df

class CategoricalEncoderTransformer(BaseEstimator, TransformerMixin):
    """Aplica One-Hot para variáveis nominais e Label Encoding para ordinais conforme lógica original."""
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.actions_: List[str] = []
        self.nominal_cols_: List[str] = []
        self.ordinal_cols_: List[str] = []
        self.label_maps_: Dict[str, Dict[str, int]] = {}
        self.top_category_maps_: Dict[str, List[str]] = {}

    def fit(self, X: pd.DataFrame, y=None):
        nominal_candidates = [
            'Dim_Lojas.Tipo_PDV', 'Dim_Lojas.CANAL_VENDA', 'Dim_Lojas.REGIAO_CHILLI',
            'Dim_Cliente.Uf_Cliente', 'Dim_Produtos.Grupo_Produto', 'Dim_Produtos.Sub_Grupo'
        ]
        self.nominal_cols_ = [c for c in nominal_candidates if c in X.columns]
        for col in self.nominal_cols_:
            categorias_unicas = X[col].nunique()
            if categorias_unicas > 10:
                top = X[col].value_counts().head(10).index.tolist()
                self.top_category_maps_[col] = top
            else:
                self.top_category_maps_[col] = []  # vazio indica sem redução
        ordinal_candidates = ['Dim_Cliente.Sexo', 'Dim_Cliente.Estado_Civil']
        self.ordinal_cols_ = [c for c in ordinal_candidates if c in X.columns]
        for col in self.ordinal_cols_:
            valores_limpos = X[col].astype(str)
            categorias = sorted(valores_limpos.unique())
            self.label_maps_[col] = {cat: idx for idx, cat in enumerate(categorias)}
        return self

    def transform(self, X: pd.DataFrame):
        df = X.copy()
        # One-Hot
        for col in self.nominal_cols_:
            if self.top_category_maps_[col]:
                top = self.top_category_maps_[col]
                df[col] = df[col].apply(lambda x: x if x in top else 'Outros')
                categorias_unicas = df[col].nunique()
            else:
                categorias_unicas = df[col].nunique()
            dummies = pd.get_dummies(df[col], prefix=col, drop_first=True)
            df = pd.concat([df, dummies], axis=1)
            self.actions_.append(f"{col}: One-Hot Encoding: {categorias_unicas} categorias → {len(dummies.columns)} colunas dummy")
        # Label Encoding
        for col in self.ordinal_cols_:
            mapping = self.label_maps_[col]
            df[f'{col}_encoded'] = df[col].astype(str).map(mapping)
            self.actions_.append(f"{col}: Label Encoding: {mapping}")
        return df

def _build_preprocessing_pipeline(verbose: bool = False) -> Any:
    """Constroi o sklearn Pipeline com os transformadores customizados na ordem original.

    Retorna instancia de sklearn.pipeline.Pipeline quando sklearn está disponível, caso contrário None.
    """
    if SkPipeline is None:
        return None
    return SkPipeline([
        ('missing', MissingValueTransformer(verbose=verbose)),
        ('outliers', OutlierTransformer(verbose=verbose)),
        ('scaling', ScalingTransformer(verbose=verbose)),
        ('encoding', CategoricalEncoderTransformer(verbose=verbose)),
    ])

# Função principal para uso direto do módulo
def main():
    """
    Função principal para executar o pré-processamento completo.
    Pode ser chamada diretamente ou usada como script.
    """

    # Configurações
    input_file = 'assets/dados.csv'
    output_file = 'assets/dados_processados.csv'

    # Executar pré-processamento
    df_original_shape = (40291, 61)  # Valores conhecidos do dataset original
    df_processed, acoes_documentadas = preprocess_data(input_file, verbose=True)

    return df_processed, acoes_documentadas

if __name__ == "__main__":
    # Executar se chamado diretamente
    main()