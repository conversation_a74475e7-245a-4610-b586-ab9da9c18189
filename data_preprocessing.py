"""
Módulo de Pré-processamento de Dados - Análise de Vendas Chilli Beans

OBJETIVO EDUCACIONAL:
Este módulo demonstra as melhores práticas de pré-processamento de dados usando
scikit-learn Pipeline, combinando transformadores built-in com transformadores
customizados quando necessário.

PIPELINE:
O pipeline segue uma sequência lógica de transformações que prepara os dados
para análises avançadas e modelagem preditiva:

1. TRATAMENTO DE VALORES AUSENTES (MissingValueTransformer)
   - Utiliza SimpleImputer do scikit-learn
   - Remove colunas com >50% de valores ausentes
   - Imputa categóricas com moda (most_frequent strategy)
   - Imputa numéricas com mediana (median strategy)

2. DETECÇÃO E TRATAMENTO DE OUTLIERS (OutlierTransformer - Custom)
   - Implementação customizada usando método IQR (Interquartile Range)
   - Estratégias adaptativas baseadas na porcentagem de outliers:
     * >10%: Cap truncation (limitação aos valores IQR)
     * 5-10%: Substituição por percentis 5% e 95%
     * 1-5%: Remoção dos registros outliers
     * <1%: Manutenção (impacto negligível)

3. NORMALIZAÇÃO E PADRONIZAÇÃO (ScalingTransformer)
   - MinMaxScaler: Normalização para escala 0-1 (colunas *_normalized)
   - StandardScaler: Padronização Z-Score μ=0, σ=1 (colunas *_standardized)
   - Preserva dados originais e adiciona versões transformadas

4. CODIFICAÇÃO DE VARIÁVEIS CATEGÓRICAS (CategoricalEncoderTransformer)
   - OneHotEncoder: Para variáveis nominais (com redução se >10 categorias)
   - LabelEncoder: Para variáveis ordinais (Sexo, Estado Civil)
   - Tratamento inteligente de categorias raras (agrupamento em 'Outros')

EXEMPLO DE USO:
```python
from data_preprocessing import preprocess_data

# Aplicar pipeline completo
df_processed, actions = preprocess_data('dados.csv', verbose=True)

# Verificar transformações aplicadas
for step, step_actions in actions.items():
    print(f"{step}: {step_actions}")
```
"""

import pandas as pd
import numpy as np
import warnings
from typing import Dict, List, Tuple

# Scikit-learn imports - transformadores built-in para máxima eficiência
from sklearn.base import BaseEstimator, TransformerMixin  # Classes base para transformadores customizados
from sklearn.pipeline import Pipeline                     # Pipeline para execução de transformações
from sklearn.impute import SimpleImputer                 # Imputação eficiente de valores ausentes
from sklearn.preprocessing import (                      # Transformadores de pré-processamento
    StandardScaler,    # Padronização Z-Score (μ=0, σ=1)
    MinMaxScaler,      # Normalização Min-Max (0-1)
    LabelEncoder       # Codificação Label para categóricas ordinais
)

warnings.filterwarnings('ignore')

def preprocess_data(csv_path: str, verbose: bool = False) -> Tuple[pd.DataFrame, Dict[str, List[str]]]:
    """
    Aplica o pipeline completo de pré-processamento aos dados de vendas.

    Esta função utiliza scikit-learn Pipeline com transformadores built-in para
    máxima eficiência, mantendo a mesma funcionalidade da implementação original.

    Args:
        csv_path (str): Caminho para o arquivo CSV com os dados
        verbose (bool): Se True, exibe informações detalhadas do processamento

    Returns:
        Tuple[pd.DataFrame, Dict[str, List[str]]]:
            - DataFrame processado
            - Dicionário com documentação das ações realizadas
    """
    # Import local para evitar dependências circulares
    from data_filtering import apply_business_filters

    # Carregar e filtrar dados usando regras de negócio
    df = apply_business_filters(csv_path, verbose=False)
    if verbose:
        print(f"Dataset carregado: {df.shape[0]:,} registros, {df.shape[1]} colunas")

    # Construir e aplicar pipeline de pré-processamento
    pipeline = _build_preprocessing_pipeline(verbose=verbose)
    df_processed = pipeline.fit_transform(df)

    # Coletar documentação das transformações aplicadas
    acoes_documentadas: Dict[str, List[str]] = {
        'missing_values': getattr(pipeline.named_steps['missing'], 'actions_', []),
        'outliers': getattr(pipeline.named_steps['outliers'], 'actions_', []),
        'normalization': getattr(pipeline.named_steps['scaling'], 'actions_', []),
        'encoding': getattr(pipeline.named_steps['encoding'], 'actions_', []),
    }

    if verbose:
        print(f"Dataset final: {df_processed.shape[0]:,} registros, {df_processed.shape[1]} colunas")
        print(f"Valores ausentes restantes: {df_processed.isnull().sum().sum()}")

    return df_processed, acoes_documentadas

class MissingValueTransformer(BaseEstimator, TransformerMixin):
    """
    Transformador eficiente para tratamento de valores ausentes.

    Utiliza SimpleImputer do scikit-learn para imputação, mantendo a lógica original:
    - Remove colunas com >50% de valores ausentes
    - Imputa variáveis categóricas com moda (most_frequent)
    - Imputa variáveis numéricas com mediana

    Attributes:
        verbose (bool): Se True, exibe informações detalhadas
        actions_ (List[str]): Lista de ações realizadas para documentação
    """

    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.actions_: List[str] = []
        self.columns_to_drop_: List[str] = []
        self.categorical_imputer_: SimpleImputer = None
        self.numerical_imputer_: SimpleImputer = None
        self.categorical_cols_: List[str] = []
        self.numerical_cols_: List[str] = []

    def fit(self, X: pd.DataFrame, y=None):
        """
        Ajusta o transformador aos dados, identificando colunas para remoção e imputação.

        Args:
            X (pd.DataFrame): Dados de entrada
            y: Ignorado (compatibilidade com sklearn)

        Returns:
            self: Retorna a instância ajustada
        """
        df = X.copy()
        missing_data = df.isnull().sum()
        missing_cols = missing_data[missing_data > 0]

        # Identificar colunas para remoção (>50% ausentes)
        for col in missing_cols.index:
            missing_percent = (missing_data[col] / len(df)) * 100
            if missing_percent > 50:
                self.columns_to_drop_.append(col)
                self.actions_.append(f"{col}: Remoção da coluna (>50% ausentes)")

        # Remover colunas identificadas para análise de imputação
        df_for_imputation = df.drop(columns=self.columns_to_drop_, errors='ignore')

        # Separar colunas categóricas e numéricas com valores ausentes
        remaining_missing = df_for_imputation.isnull().sum()
        remaining_missing_cols = remaining_missing[remaining_missing > 0]

        for col in remaining_missing_cols.index:
            if df_for_imputation[col].dtype == 'object':
                self.categorical_cols_.append(col)
            else:
                self.numerical_cols_.append(col)

        # Configurar e ajustar imputadores se necessário
        if self.categorical_cols_:
            self.categorical_imputer_ = SimpleImputer(strategy='most_frequent')
            self.categorical_imputer_.fit(df_for_imputation[self.categorical_cols_])

            # Documentar ações de imputação categórica
            for col in self.categorical_cols_:
                mode_value = df_for_imputation[col].mode()[0] if not df_for_imputation[col].mode().empty else 'Não informado'
                self.actions_.append(f"{col}: Imputação com moda '{mode_value}'")

        if self.numerical_cols_:
            self.numerical_imputer_ = SimpleImputer(strategy='median')
            self.numerical_imputer_.fit(df_for_imputation[self.numerical_cols_])

            # Documentar ações de imputação numérica
            for col in self.numerical_cols_:
                median_value = df_for_imputation[col].median()
                self.actions_.append(f"{col}: Imputação com mediana {median_value:.2f}")

        # Documentar se não há valores ausentes
        if len(missing_cols) == 0:
            self.actions_.append("Nenhuma ação necessária - dataset sem valores ausentes")

        return self

    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Aplica as transformações de valores ausentes aos dados.

        Args:
            X (pd.DataFrame): Dados de entrada

        Returns:
            pd.DataFrame: Dados com valores ausentes tratados
        """
        df = X.copy()

        # Remover colunas com >50% ausentes
        if self.columns_to_drop_:
            df = df.drop(columns=self.columns_to_drop_, errors='ignore')

        # Aplicar imputação categórica
        if self.categorical_imputer_ is not None and self.categorical_cols_:
            categorical_cols_present = [col for col in self.categorical_cols_ if col in df.columns]
            if categorical_cols_present:
                df[categorical_cols_present] = self.categorical_imputer_.transform(df[categorical_cols_present])

        # Aplicar imputação numérica
        if self.numerical_imputer_ is not None and self.numerical_cols_:
            numerical_cols_present = [col for col in self.numerical_cols_ if col in df.columns]
            if numerical_cols_present:
                df[numerical_cols_present] = self.numerical_imputer_.transform(df[numerical_cols_present])

        return df

class OutlierTransformer(BaseEstimator, TransformerMixin):
    """
    Transformador customizado para detecção e tratamento inteligente de outliers.

    METODOLOGIA - IQR (Interquartile Range):
    O método IQR é uma técnica robusta para detecção de outliers que:
    1. Calcula Q1 (25º percentil) e Q3 (75º percentil)
    2. Define IQR = Q3 - Q1 (amplitude interquartil)
    3. Identifica outliers como valores fora de [Q1 - 1.5*IQR, Q3 + 1.5*IQR]

    ESTRATÉGIAS ADAPTATIVAS DE TRATAMENTO:
    - >10% outliers: Cap truncation (limitação aos limites IQR)
    - 5-10% outliers: Substituição por percentis 5% e 95%
    - 1-5% outliers: Remoção dos registros (pode alterar shape do dataset)
    - <1% outliers: Manutenção (impacto considerado negligível)

    NOTA IMPORTANTE:
    Este transformador pode alterar o número de linhas do dataset (remoção de outliers),
    o que é aceitável neste contexto de preparação para modelagem preditiva.

    Attributes:
        numeric_columns (List[str]): Colunas numéricas para análise de outliers
        verbose (bool): Se True, exibe informações detalhadas
        actions_ (List[str]): Documentação das ações realizadas
        thresholds_ (Dict): Limites IQR calculados para cada coluna
    """

    def __init__(self, numeric_columns: List[str] | None = None, verbose: bool = False):
        self.numeric_columns = numeric_columns
        self.verbose = verbose
        self.actions_: List[str] = []
        self.thresholds_: Dict[str, Tuple[float, float]] = {}

    def _detect_iqr(self, s: pd.Series) -> Tuple[float, float]:
        """
        Calcula os limites IQR para detecção de outliers.

        Fórmula IQR:
        - Q1 = 25º percentil
        - Q3 = 75º percentil
        - IQR = Q3 - Q1
        - Limites = [Q1 - 1.5*IQR, Q3 + 1.5*IQR]

        Args:
            s (pd.Series): Série numérica para análise

        Returns:
            Tuple[float, float]: (limite_inferior, limite_superior)
        """
        Q1, Q3 = s.quantile(0.25), s.quantile(0.75)
        IQR = Q3 - Q1
        lower, upper = Q1 - 1.5 * IQR, Q3 + 1.5 * IQR
        return lower, upper

    def fit(self, X: pd.DataFrame, y=None):
        df = X
        cols = self.numeric_columns or ['Quantidade', 'Preco_Custo', 'Valor_Total', 'Preco_Varejo', 'Frete', 'Desconto']
        self.cols_ = [c for c in cols if c in df.columns]
        # Guardar limites max e min para logging apenas
        for col in self.cols_:
            lower, upper = self._detect_iqr(df[col])
            self.thresholds_[col] = (lower, upper)
        return self

    def transform(self, X: pd.DataFrame):
        df = X.copy()
        for col in self.cols_:
            lower, upper = self.thresholds_[col]
            outliers_mask = (df[col] < lower) | (df[col] > upper)
            percentual = outliers_mask.mean() * 100
            if percentual > 10:
                df[col] = np.where(df[col] < lower, lower, df[col])
                df[col] = np.where(df[col] > upper, upper, df[col])
                self.actions_.append(f"{col}: Cap truncation nos limites [{lower:.2f}, {upper:.2f}] ({percentual:.1f}% outliers)")
            elif percentual > 5:
                p5 = df[col].quantile(0.05)
                p95 = df[col].quantile(0.95)
                df[col] = np.where(df[col] < p5, p5, df[col])
                df[col] = np.where(df[col] > p95, p95, df[col])
                self.actions_.append(f"{col}: Substituição por percentis 5% ({p5:.2f}) e 95% ({p95:.2f}) ({percentual:.1f}% outliers)")
            elif percentual > 1:
                before = len(df)
                df = df[~outliers_mask]
                removed = before - len(df)
                self.actions_.append(f"{col}: Remoção de {removed} registros ({percentual:.1f}% outliers)")
            else:
                self.actions_.append(f"{col}: Mantidos ({percentual:.1f}% outliers - impacto negligível)")
        return df

class ScalingTransformer(BaseEstimator, TransformerMixin):
    """
    Transformador eficiente para normalização e padronização de dados.

    Utiliza MinMaxScaler e StandardScaler do scikit-learn para aplicar:
    - Normalização Min-Max (0-1) criando colunas *_normalized
    - Padronização Z-Score (μ=0, σ=1) criando colunas *_standardized

    Mantém a mesma funcionalidade da implementação original com melhor performance.

    Attributes:
        verbose (bool): Se True, exibe informações detalhadas
        actions_ (List[str]): Lista de ações realizadas para documentação
    """

    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.actions_: List[str] = []
        self.cols_: List[str] = []
        self.minmax_scaler_: MinMaxScaler = None
        self.standard_scaler_: StandardScaler = None
        self.original_stats_: Dict[str, Dict[str, float]] = {}

    def fit(self, X: pd.DataFrame, y=None):
        """
        Ajusta os scalers aos dados numéricos especificados.

        Args:
            X (pd.DataFrame): Dados de entrada
            y: Ignorado (compatibilidade com sklearn)

        Returns:
            self: Retorna a instância ajustada
        """
        # Definir colunas numéricas para escalonamento (mesmas da implementação original)
        target_cols = ['Quantidade', 'Preco_Custo', 'Valor_Total', 'Preco_Varejo', 'Frete', 'Desconto']
        self.cols_ = [col for col in target_cols if col in X.columns]

        if self.cols_:
            # Armazenar estatísticas originais para documentação
            for col in self.cols_:
                series = X[col]
                self.original_stats_[col] = {
                    'min': float(series.min()),
                    'max': float(series.max()),
                    'mean': float(series.mean()),
                    'std': float(series.std())
                }

            # Ajustar scalers com dados numéricos
            numeric_data = X[self.cols_]

            # MinMaxScaler para normalização (0-1)
            self.minmax_scaler_ = MinMaxScaler()
            self.minmax_scaler_.fit(numeric_data)

            # StandardScaler para padronização Z-Score
            self.standard_scaler_ = StandardScaler()
            self.standard_scaler_.fit(numeric_data)

        return self

    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Aplica normalização Min-Max e padronização Z-Score aos dados.

        Args:
            X (pd.DataFrame): Dados de entrada

        Returns:
            pd.DataFrame: Dados com colunas normalizadas e padronizadas adicionadas
        """
        df = X.copy()

        if self.cols_ and self.minmax_scaler_ is not None and self.standard_scaler_ is not None:
            # Verificar quais colunas estão presentes
            present_cols = [col for col in self.cols_ if col in df.columns]

            if present_cols:
                # Aplicar normalização Min-Max
                normalized_data = self.minmax_scaler_.transform(df[present_cols])
                for i, col in enumerate(present_cols):
                    df[f'{col}_normalized'] = normalized_data[:, i]

                    # Documentar ação de normalização
                    stats = self.original_stats_[col]
                    if stats['max'] != stats['min']:
                        self.actions_.append(
                            f"{col}: Normalização Min-Max (0-1): [{stats['min']:.2f}, {stats['max']:.2f}] → [0, 1]"
                        )
                    else:
                        self.actions_.append(f"{col}: Normalização Min-Max: valores constantes → 0")

                # Aplicar padronização Z-Score
                standardized_data = self.standard_scaler_.transform(df[present_cols])
                for i, col in enumerate(present_cols):
                    df[f'{col}_standardized'] = standardized_data[:, i]

                    # Documentar ação de padronização
                    stats = self.original_stats_[col]
                    if stats['std'] != 0:
                        self.actions_.append(
                            f"{col}: Padronização Z-Score: μ={stats['mean']:.2f}, σ={stats['std']:.2f} → μ≈0, σ≈1"
                        )
                    else:
                        self.actions_.append(f"{col}: Padronização Z-Score: desvio zero → 0")

        return df

class CategoricalEncoderTransformer(BaseEstimator, TransformerMixin):
    """
    Transformador eficiente para codificação de variáveis categóricas.

    Utiliza OneHotEncoder e LabelEncoder do scikit-learn para aplicar:
    - One-Hot Encoding para variáveis nominais (com redução de categorias se >10)
    - Label Encoding para variáveis ordinais

    Mantém a mesma funcionalidade da implementação original com melhor performance.

    Attributes:
        verbose (bool): Se True, exibe informações detalhadas
        actions_ (List[str]): Lista de ações realizadas para documentação
    """

    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.actions_: List[str] = []
        self.nominal_cols_: List[str] = []
        self.ordinal_cols_: List[str] = []
        self.label_encoders_: Dict[str, LabelEncoder] = {}
        self.top_category_maps_: Dict[str, List[str]] = {}
        self.category_counts_: Dict[str, int] = {}

    def fit(self, X: pd.DataFrame, y=None):
        """
        Ajusta os encoders às variáveis categóricas especificadas.

        Args:
            X (pd.DataFrame): Dados de entrada
            y: Ignorado (compatibilidade com sklearn)

        Returns:
            self: Retorna a instância ajustada
        """
        # Definir colunas nominais (mesmas da implementação original)
        nominal_candidates = [
            'Dim_Lojas.Tipo_PDV', 'Dim_Lojas.CANAL_VENDA', 'Dim_Lojas.REGIAO_CHILLI',
            'Dim_Cliente.Uf_Cliente', 'Dim_Produtos.Grupo_Produto', 'Dim_Produtos.Sub_Grupo'
        ]
        self.nominal_cols_ = [col for col in nominal_candidates if col in X.columns]

        # Processar colunas nominais para análise de categorias
        for col in self.nominal_cols_:
            categorias_unicas = X[col].nunique()
            self.category_counts_[col] = categorias_unicas

            # Reduzir categorias se >10 (manter top 10 + 'Outros')
            if categorias_unicas > 10:
                top_categories = X[col].value_counts().head(10).index.tolist()
                self.top_category_maps_[col] = top_categories
            else:
                self.top_category_maps_[col] = []  # Sem redução

        # Definir e ajustar colunas ordinais para Label Encoding
        ordinal_candidates = ['Dim_Cliente.Sexo', 'Dim_Cliente.Estado_Civil']
        self.ordinal_cols_ = [col for col in ordinal_candidates if col in X.columns]

        for col in self.ordinal_cols_:
            # Criar e ajustar LabelEncoder para cada coluna ordinal
            label_encoder = LabelEncoder()
            valores_limpos = X[col].astype(str)
            label_encoder.fit(valores_limpos)
            self.label_encoders_[col] = label_encoder

        return self

    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Aplica codificação One-Hot e Label Encoding aos dados.

        Args:
            X (pd.DataFrame): Dados de entrada

        Returns:
            pd.DataFrame: Dados com variáveis categóricas codificadas
        """
        df = X.copy()

        # Aplicar One-Hot Encoding para variáveis nominais usando pandas get_dummies
        # (para manter compatibilidade com implementação original)
        for col in self.nominal_cols_:
            if col in df.columns:
                # Aplicar redução de categorias se necessário
                if self.top_category_maps_[col]:
                    top_categories = self.top_category_maps_[col]
                    df[col] = df[col].apply(lambda x: x if x in top_categories else 'Outros')
                    final_categories = len(top_categories) + 1  # +1 para 'Outros'
                else:
                    final_categories = self.category_counts_[col]

                # Aplicar One-Hot Encoding usando pandas (compatível com original)
                dummies = pd.get_dummies(df[col], prefix=col, drop_first=True)
                df = pd.concat([df, dummies], axis=1)

                # Documentar ação
                if self.top_category_maps_[col]:
                    self.actions_.append(
                        f"{col}: One-Hot Encoding: {self.category_counts_[col]} → {final_categories} categorias (top 10 + 'Outros') → {len(dummies.columns)} colunas dummy"
                    )
                else:
                    self.actions_.append(
                        f"{col}: One-Hot Encoding: {final_categories} categorias → {len(dummies.columns)} colunas dummy"
                    )

        # Aplicar Label Encoding para variáveis ordinais
        for col in self.ordinal_cols_:
            if col in df.columns and col in self.label_encoders_:
                label_encoder = self.label_encoders_[col]
                valores_limpos = df[col].astype(str)

                # Aplicar encoding, tratando valores desconhecidos
                try:
                    df[f'{col}_encoded'] = label_encoder.transform(valores_limpos)
                except ValueError:
                    # Lidar com categorias não vistas durante fit
                    encoded_values = []
                    for value in valores_limpos:
                        if value in label_encoder.classes_:
                            encoded_values.append(label_encoder.transform([value])[0])
                        else:
                            encoded_values.append(-1)  # Valor para categoria desconhecida
                    df[f'{col}_encoded'] = encoded_values

                # Documentar ação
                mapping = {cls: idx for idx, cls in enumerate(label_encoder.classes_)}
                self.actions_.append(f"{col}: Label Encoding: {mapping}")

        return df

def _build_preprocessing_pipeline(verbose: bool = False) -> Pipeline:
    """
    Constrói o pipeline de pré-processamento usando scikit-learn Pipeline.

    O pipeline aplica as transformações na seguinte ordem:
    1. Tratamento de valores ausentes (SimpleImputer via MissingValueTransformer)
    2. Detecção e tratamento de outliers (Custom OutlierTransformer)
    3. Normalização e padronização (MinMaxScaler + StandardScaler via ScalingTransformer)
    4. Codificação categórica (OneHotEncoder + LabelEncoder via CategoricalEncoderTransformer)

    Args:
        verbose (bool): Se True, os transformadores exibem informações detalhadas

    Returns:
        Pipeline: Pipeline configurado com todos os transformadores
    """
    return Pipeline([
        ('missing', MissingValueTransformer(verbose=verbose)),
        ('outliers', OutlierTransformer(verbose=verbose)),
        ('scaling', ScalingTransformer(verbose=verbose)),
        ('encoding', CategoricalEncoderTransformer(verbose=verbose)),
    ])

def main():
    """
    Função principal para executar o pré-processamento completo.

    Demonstra o uso do pipeline de pré-processamento refatorado,
    aplicando todas as transformações aos dados de vendas.

    Returns:
        Tuple[pd.DataFrame, Dict[str, List[str]]]:
            - DataFrame processado
            - Dicionário com documentação das transformações
    """
    # Configuração do arquivo de entrada
    input_file = 'assets/dados.csv'

    # Executar pipeline de pré-processamento completo
    df_processed, acoes_documentadas = preprocess_data(input_file, verbose=True)

    return df_processed, acoes_documentadas

if __name__ == "__main__":
    # Executar se chamado diretamente
    main()